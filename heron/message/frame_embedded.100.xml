<?xml version="1.0" encoding="UTF-8"?>

<msg>
    <message name="FrameEmbeddedInfo" maxsize="8192" strategy="1" id="100">
        <variable name="warp_mode" type="int32_t"/>
        <variable name="pose_guid_high" type="uint64_t" default="0" />
        <variable name="pose_guid_low" type="uint64_t" default="0" />
        <variable name="frame_buffer_mode" type="int32_t" default="0" />
        <variable name="device_pose_time" type="uint64_t" default="0" />
        <variable name="buffer_meta_datas" type="std::vector&lt;BufferMetaData&gt;"/>
        <variable name="frame_number" type="uint64_t"/>
        <variable name="embedded_device_time" type="uint64_t" default="0" />
        <variable name="sys_pose_time" type="uint64_t" default="0" />
        <variable name="start_render_time" type="uint64_t" default="0" />
        <variable name="submit_time" type="uint64_t" default="0" />
    </message>
</msg>

