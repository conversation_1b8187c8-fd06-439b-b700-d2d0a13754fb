#pragma once

#include <heron/util/types.h>

#include <warpcore/warp_types.h>
using namespace warp_core;

namespace heron
{
    struct BufferMetadataTwin
    {
        int32_t target;
        Transform pose;
        Fov4f fov;
        Vector3f plane_point;
        Vector3f plane_normal;
    };
    struct FrameMetaInfoTwin
    {
        uint64_t frame_number;
        LateStageReprojectionMode lsr_mode; // XXX: to differentiate from gdc_frame_config.warp_mode
        uint64_t pose_guid_high;
        uint64_t pose_guid_low;
        int32_t framebuffer_mode; // framebuffer is a single word, but in some include files it's called frame_buffer_mode
        uint64_t device_pose_time;
        uint64_t sys_pose_time;
        uint64_t data_device_time;
        std::vector<BufferMetadataTwin> metadata_vec;
        uint64_t start_render_time;
        uint64_t submit_time;
    };

    struct FrameEmbeddedInfoTwin
    {
        LateStageReprojectionMode lsr_mode; // XXX: to differentiate from gdc_frame_config.warp_mode
        uint64_t pose_guid_high;
        uint64_t pose_guid_low;
        int32_t framebuffer_mode; // framebuffer is a single word, but in some include files it's called frame_buffer_mode
        uint64_t device_pose_time;
        std::vector<BufferMetadataTwin> metadata_vec;
        uint64_t frame_number;
        uint64_t embedded_device_time;
        uint64_t sys_pose_time;
        uint64_t start_render_time;
        uint64_t submit_time;
    };

    struct FrameEmbeddedInfoSimpleTwin
    {
        uint64_t frame_number;
        uint64_t embedded_device_time;
    };
}