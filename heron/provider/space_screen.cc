#include <heron/env/system_config.h>
#include <heron/dispatch/dispatcher_wrapper.h>
#include <heron/interface/include/channel/nr_plugin_glasses_types.h>
#include <heron/model/model_manager.h>
#include <heron/model/permanent_config.h>
#include <heron/control/control_display.h>
#include <heron/util/nr_type_converter.h>
#include <heron/util/debug.h>
#include <heron/util/log.h>

#include <framework/util/util.h>

using namespace heron;
using namespace heron::model;

/*
    空间屏相关
*/
static NRPluginResult SetDpInputMode(NRPluginHandle handle, NRDpInputMode mode);
static NRPluginResult SetLocalPerceptionType(NRPluginHandle handle, NRPerceptionType type);
/*
    返回当前模式下的画布尺寸，单位是m。不同模式下有自己的画布尺寸，比如odof或者小窗
*/
static NRPluginResult GetCanvasDiagonalSize(NRPluginHandle handle, float *out_size);
static NRPluginResult UpdateCanvasSize(NRPluginHandle handle, NROperationType operation_type, NRStepType step_type);
/*
    返回当前模式下的画布距离，单位是m。不同模式下有自己的画布距离，比如odof或者小窗
*/
static NRPluginResult GetCanvasDepth(NRPluginHandle handle, float *out_depth);
static NRPluginResult UpdateCanvasDepth(NRPluginHandle handle, NROperationType operation_type, NRStepType step_type);
static NRPluginResult GetSpaceMode(NRPluginHandle handle, NRSpaceMode *out_space_mode);
static NRPluginResult SetSpaceMode(NRPluginHandle handle, NRSpaceMode space_mode);
static NRPluginResult GetPupilLevelCount(NRPluginHandle handle, int32_t *out_level);
static NRPluginResult GetPupilLevel(NRPluginHandle handle, int32_t *out_level);
static NRPluginResult SetPupilLevel(NRPluginHandle handle, int32_t level);
static NRPluginResult StartPupilAdjust(NRPluginHandle handle);
static NRPluginResult StopPupilAdjust(NRPluginHandle handle);
static NRPluginResult GetThumbnailPositionType(NRPluginHandle handle, NRThumbnailPositionType *out_thumbnail_position);
static NRPluginResult SetThumbnailPositionType(NRPluginHandle handle, NRThumbnailPositionType thumbnail_position);
static NRPluginResult Recenter(NRPluginHandle handle, uint32_t deep_value);
static NRPluginResult GetLookingAtArea(NRPluginHandle handle, NRCanvasAreaType *type);
static NRPluginResult GetUltraWideHeadArea(NRPluginHandle handle, NRCanvasAreaType *type);
static NRPluginResult GetDisplayDiagonalFov(NRPluginHandle handle, float *out_diagonal_fov);
static NRPluginResult GetTargetCanvasDiagonalSize(NRPluginHandle handle, NREdid target_dp_edid, float *out_size);
static NRPluginResult GetCanvasOptimalValues(NRPluginHandle handle, NRVector3f *out_rotation_value, NRVector3f *out_translation_value);
static NRPluginResult UpdateCanvasOptimalValue(NRPluginHandle handle, NRDofType dof_type, NROperationType operation_type, NRStepType step_type);
static NRPluginResult ResetCanvasOptimalValues(NRPluginHandle handle);
static NRPluginResult GetCanvasDepthRange(NRPluginHandle handle, float *out_min_depth, float *out_max_depth);
static NRPluginResult GetCanvasDiagonalSizeRange(NRPluginHandle handle, float *out_min_size, float *out_max_size);
static NRPluginResult GetCanvasSizeLevelCount(NRPluginHandle handle, uint32_t *out_level_count);
static NRPluginResult GetRoundedCanvasSizeLevel(NRPluginHandle handle, uint32_t *out_level);
static NRPluginResult NotifyHandData(NRPluginHandle handle, uint64_t hmd_time_nanos, const NRHandData *hand_data, uint32_t hand_num);

/// global variables
static std::vector<void *> s_space_screen_provider{
    (void *)&SetDpInputMode,
    (void *)&SetLocalPerceptionType,
    (void *)&GetCanvasDiagonalSize,
    (void *)&UpdateCanvasSize,
    (void *)&GetCanvasDepth,
    (void *)&UpdateCanvasDepth,
    (void *)&GetSpaceMode,
    (void *)&SetSpaceMode,
    (void *)&GetPupilLevelCount,
    (void *)&GetPupilLevel,
    (void *)&SetPupilLevel,
    (void *)&StartPupilAdjust,
    (void *)&StopPupilAdjust,
    (void *)&GetThumbnailPositionType,
    (void *)&SetThumbnailPositionType,
    (void *)&Recenter,
    (void *)&GetLookingAtArea,
    (void *)&GetUltraWideHeadArea,
    (void *)&GetDisplayDiagonalFov,
    (void *)GetTargetCanvasDiagonalSize,
    (void *)&GetCanvasOptimalValues,
    (void *)&UpdateCanvasOptimalValue,
    (void *)&ResetCanvasOptimalValues,
    //    (void *)&GetCanvasDepthRange,
    //    (void *)&GetCanvasDiagonalSizeRange,
    //    (void *)&GetCanvasSizeLevelCount,
    //    (void *)&GetRoundedCanvasSizeLevel,
    (void *)&NotifyHandData,
};

const void *GetSpaceScreenProvider(uint32_t &size) // size in bytes
{
    size = s_space_screen_provider.size() * sizeof(void *);
    return s_space_screen_provider.data();
}

NRPluginResult SetDpInputMode(NRPluginHandle, NRDpInputMode mode)
{
    HERON_LOG_DEBUG("{} mode:{}", __FUNCTION__, mode);
    ModelManager::GetInstance()->SetDpInputMode((DpInputMode)mode);
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult SetLocalPerceptionType(NRPluginHandle, NRPerceptionType type)
{
    HERON_LOG_DEBUG("{} type:{}", __FUNCTION__, type);
    if (ModelManager::GetInstance()->GetLocalPerceptionType() == (PerceptionType)type)
        return NR_PLUGIN_RESULT_SUCCESS;
    control::DisplayCtrl::GetInstance()->InvalidateLatestHeadTransform();
    ModelManager::GetInstance()->SetLocalPerceptionType((PerceptionType)type);
    if (type == NR_PERCEPTION_TYPE_3DOF || type == NR_PERCEPTION_TYPE_6DOF || type == NR_PERCEPTION_TYPE_3DOF_OPTI)
        control::DisplayCtrl::GetInstance()->NeedRecenter();
    else
        control::DisplayCtrl::GetInstance()->ResetRecenterTransform();
    return NR_PLUGIN_RESULT_SUCCESS;
}
/*
    返回当前模式下的画布尺寸，单位是m。不同模式下有自己的画布尺寸，比如0dof或者小窗
*/
NRPluginResult GetCanvasDiagonalSize(NRPluginHandle, float *out_size)
{
    float diagonal_size_meters = ModelManager::GetInstance()->GetSpaceScreenStatus()->GetTargetDiagonalSizeMeters();
    *out_size = diagonal_size_meters * ModelManager::GetInstance()->GetMarketingSizeFactor();
    HERON_LOG_DEBUG("{} marketing_size_factor: {} diagonal_size_meters:{}", __FUNCTION__,
                    ModelManager::GetInstance()->GetMarketingSizeFactor(), diagonal_size_meters);
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult UpdateCanvasSize(NRPluginHandle, NROperationType operation_type, NRStepType step_type)
{
    HERON_LOG_DEBUG("{} operation:{} step:{}", __FUNCTION__, operation_type, step_type);
    ModelManager::GetInstance()->UpdateCanvasSizeAndSave((OperationType)operation_type, (StepType)step_type);
    return NR_PLUGIN_RESULT_SUCCESS;
}
/*
    返回当前模式下的画布距离，单位是m。不同模式下有自己的画布距离，比如0dof或者小窗
*/
NRPluginResult GetCanvasDepth(NRPluginHandle, float *out_depth)
{
    HERON_LOG_DEBUG("{}", __FUNCTION__);
    *out_depth = ModelManager::GetInstance()->GetTargetDepth();
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult UpdateCanvasDepth(NRPluginHandle, NROperationType operation_type, NRStepType step_type)
{
    HERON_LOG_DEBUG("{} operation:{} step:{}", __FUNCTION__, operation_type, step_type);
    ModelManager::GetInstance()->UpdateCanvasDepthAndSave((OperationType)operation_type, (StepType)step_type);
    return NR_PLUGIN_RESULT_SUCCESS;
}
/*
    <pfunction name="DecreaseCanvasDepth">
    <param name="handle" type="NRPluginHandle"/>
    <param name="step_type" type="NRStepType"/>
    </pfunction>
*/
NRPluginResult GetSpaceMode(NRPluginHandle, NRSpaceMode *out_space_mode)
{
    SpaceMode mode = ModelManager::GetInstance()->GetSpaceMode();
    *out_space_mode = (NRSpaceMode)mode;
    // HERON_LOG_DEBUG("{}: {}", __FUNCTION__, mode);
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult SetSpaceMode(
    NRPluginHandle,
    NRSpaceMode space_mode)
{
    HERON_LOG_DEBUG("{}: {}", __FUNCTION__, space_mode);
    ModelManager::GetInstance()->SetSpaceModeAndSave((SpaceMode)space_mode);
    float min_float, max_float;
    return NR_PLUGIN_RESULT_SUCCESS;
}
/*

    瞳距调节

*/
NRPluginResult GetPupilLevelCount(
    NRPluginHandle handle,
    int32_t *out_level)
{
    *out_level = PermanentConfig::GetInstance()->GetPupilAdjustmentPixelArraySize() * 2 - 1;
    HERON_LOG_DEBUG("{} {}", __FUNCTION__, *out_level);
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult GetPupilLevel(
    NRPluginHandle handle,
    int32_t *out_level)
{
    *out_level = PermanentConfig::GetInstance()->GetPupilLevelIndexSaved();
    HERON_LOG_DEBUG("{} {}", __FUNCTION__, *out_level);
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult SetPupilLevel(
    NRPluginHandle,
    int32_t level)
{
    HERON_LOG_DEBUG("{} level:{}", __FUNCTION__, level);
    if (!ModelManager::GetInstance()->SetPupilLevelAndSave(level))
        return NR_PLUGIN_RESULT_FAILURE;
    return NR_PLUGIN_RESULT_SUCCESS;
}
/*

    进入/退出瞳距调节页面

*/
NRPluginResult StartPupilAdjust(
    NRPluginHandle)
{
    HERON_LOG_DEBUG("{}", __FUNCTION__);
    ModelManager::GetInstance()->SetPupilAdjustFlag(true);
    SetPupilLevel(0, PermanentConfig::GetInstance()->GetPupilLevelIndexSaved());
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult StopPupilAdjust(NRPluginHandle handle)
{
    HERON_LOG_DEBUG("{}", __FUNCTION__);
    Recenter(handle, 0); // recenter before set pupil adjust flag to false
    ModelManager::GetInstance()->SetPupilAdjustFlag(false);
    return NR_PLUGIN_RESULT_SUCCESS;
}
/*

    小窗位置

*/
NRPluginResult GetThumbnailPositionType(
    NRPluginHandle,
    NRThumbnailPositionType *out_thumbnail_position)
{
    HERON_LOG_DEBUG("{}", __FUNCTION__);
    *out_thumbnail_position = (NRThumbnailPositionType)PermanentConfig::GetInstance()->GetThumbnailPositionTypeSaved();
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult SetThumbnailPositionType(
    NRPluginHandle,
    NRThumbnailPositionType thumbnail_position)
{
    HERON_LOG_DEBUG("{}", __FUNCTION__);
    ModelManager::GetInstance()->SetThumbnailPositionTypeAndSave((ThumbnailPositionType)thumbnail_position);
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult Recenter(NRPluginHandle, uint32_t)
{
    HERON_LOG_DEBUG("{}", __FUNCTION__);
    SpaceMode mode = ModelManager::GetInstance()->GetSpaceMode();
    if (mode != SPACE_MODE_HOVER && mode != SPACE_MODE_ULTRA_WIDE)
    {
        HERON_LOG_DEBUG("in space_mode:{}, no need to recenter", mode);
        return NR_PLUGIN_RESULT_SUCCESS;
    }
    control::DisplayCtrl::GetInstance()->NeedRecenter();
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult GetLookingAtArea(NRPluginHandle handle, NRCanvasAreaType *type)
{
    // HERON_LOG_TRACE("{}", __FUNCTION__);
    *type = NR_CANVAS_AREA_UNKNOWN;
    bool is_looking_at_inner, is_looking_at_outer;
    // type is UNKNOWN if failed to get head pose
    if (!control::DisplayCtrl::GetInstance()->IsLookingAt(is_looking_at_inner, is_looking_at_outer))
        return NR_PLUGIN_RESULT_SUCCESS;
    if (is_looking_at_inner)
        *type = NR_CANVAS_AREA_INSIDE_IMMERSIVE_BOUND;
    else if (is_looking_at_outer)
        *type = NR_CANVAS_AREA_BETWEEN_IMMERSIVE_AND_INTERACTION_BOUND;
    else
        *type = NR_CANVAS_AREA_OUTSIDE_INTERACTION_BOUND;
    if (DebugManager::GetInstance()->debug_log.focus_judger)
    {
        HERON_LOG_DEBUG("IsLookingAt inner:{} outer:{} type:{}", is_looking_at_inner, is_looking_at_outer, *type);
    }
    return NR_PLUGIN_RESULT_SUCCESS;
}

static NRPluginResult GetUltraWideHeadArea(NRPluginHandle handle, NRCanvasAreaType *type)
{
    // HERON_LOG_TRACE("{}", __FUNCTION__);
    *type = NR_CANVAS_AREA_UNKNOWN;
    if (ModelManager::GetInstance()->GetSpaceMode() != SPACE_MODE_ULTRA_WIDE)
        return NR_PLUGIN_RESULT_SUCCESS;
    bool inner, outer;
    // type is UNKNOWN if failed to get head pose
    if (!control::DisplayCtrl::GetInstance()->InCylinder(inner, outer))
        return NR_PLUGIN_RESULT_SUCCESS;
    // HERON_LOG_TRACE("InCylinder inner:{} outer:{}", inner, outer);
    if (inner)
        *type = NR_CANVAS_AREA_INSIDE_IMMERSIVE_BOUND;
    else if (outer)
        *type = NR_CANVAS_AREA_BETWEEN_IMMERSIVE_AND_INTERACTION_BOUND;
    else
        *type = NR_CANVAS_AREA_OUTSIDE_INTERACTION_BOUND;
    return NR_PLUGIN_RESULT_SUCCESS;
}

static NRPluginResult GetDisplayDiagonalFov(NRPluginHandle handle, float *out_diagonal_fov)
{
    if (!out_diagonal_fov)
    {
        HERON_LOG_ERROR("{} out_diagonal_fov is null", __FUNCTION__);
        return NR_PLUGIN_RESULT_FAILURE;
    }
    *out_diagonal_fov = ModelManager::GetInstance()->GetSpaceScreenStatus()->GetTargetFovFactorOnQuadBase();
    HERON_LOG_DEBUG("{} fov_factor:{}", __FUNCTION__, *out_diagonal_fov);
    return NR_PLUGIN_RESULT_SUCCESS;
}

static NRPluginResult GetTargetCanvasDiagonalSize(NRPluginHandle handle, NREdid target_dp_edid, float *out_size)
{
    if (!out_size)
    {
        HERON_LOG_ERROR("{} out_size is null", __FUNCTION__);
        return NR_PLUGIN_RESULT_FAILURE;
    }
    float width_factor = 1.0f;
    ModelManager *p_mm = ModelManager::GetInstance();
    if (p_mm->GetSpaceMode() == SPACE_MODE_ULTRA_WIDE)
    {
        Vector2i default_src_size_pixel = p_mm->GetDefaultDpSrcSizePixel();
        Vector2i target_src_size_pixel;
        EdidToSizePixel(target_src_size_pixel, (Edid)target_dp_edid);
        width_factor = (float)target_src_size_pixel.x() / default_src_size_pixel.x();
    }
    float diagonal_size_meters = p_mm->GetSpaceScreenStatus()->GetTargetDiagonalSizeMeters(width_factor);
    *out_size = diagonal_size_meters * p_mm->GetMarketingSizeFactor();
    HERON_LOG_DEBUG("{} marketing_factor: {} out_size_meters:{} width_factor:{} space_mode:{}", __FUNCTION__,
                    p_mm->GetMarketingSizeFactor(), *out_size, width_factor, p_mm->GetSpaceMode());
    return NR_PLUGIN_RESULT_SUCCESS;
}

static NRPluginResult GetCanvasOptimalValues(NRPluginHandle handle, NRVector3f *out_rotation_value, NRVector3f *out_translation_value)
{
    if (!out_rotation_value || !out_translation_value)
    {
        HERON_LOG_ERROR("{} out_rotation_value or out_translation_value is null", __FUNCTION__);
        return NR_PLUGIN_RESULT_FAILURE;
    }
    Vector3f rotation_xyz, translation_xyz;
    ModelManager::GetInstance()->GetCanvasModelTransform(rotation_xyz, translation_xyz);
    ConvertToNRVector3f(*out_rotation_value, rotation_xyz);
    ConvertToNRVector3f(*out_translation_value, translation_xyz);
    return NR_PLUGIN_RESULT_SUCCESS;
}

static NRPluginResult UpdateCanvasOptimalValue(NRPluginHandle handle, NRDofType dof_type, NROperationType operation_type, NRStepType step_type)
{
    HERON_LOG_DEBUG("{} dof:{} operation:{} step:{}", __FUNCTION__, dof_type, operation_type, step_type);
    ModelManager::GetInstance()->UpdateCanvasModelTransformAndSave((DofType)dof_type, (OperationType)operation_type, (StepType)step_type);
    return NR_PLUGIN_RESULT_SUCCESS;
}

static NRPluginResult ResetCanvasOptimalValues(NRPluginHandle handle)
{
    HERON_LOG_DEBUG("{}", __FUNCTION__);
    ModelManager::GetInstance()->ResetCanvasModelTransformAndSave();
    return NR_PLUGIN_RESULT_SUCCESS;
}

static NRPluginResult GetCanvasDepthRange(NRPluginHandle handle, float *out_min_depth, float *out_max_depth)
{
    HERON_LOG_DEBUG("{}", __FUNCTION__);
    if (!out_min_depth || !out_max_depth)
    {
        HERON_LOG_ERROR("{} out_min_depth or out_max_depth is null", __FUNCTION__);
        return NR_PLUGIN_RESULT_FAILURE;
    }
    ModelManager::GetInstance()->GetCanvasDepthRange(out_min_depth, out_max_depth);
    HERON_LOG_DEBUG("{} min:{} max:{}", __FUNCTION__, *out_min_depth, *out_max_depth);
    return NR_PLUGIN_RESULT_SUCCESS;
}

static NRPluginResult GetCanvasDiagonalSizeRange(NRPluginHandle handle, float *out_min_size, float *out_max_size)
{
    HERON_LOG_DEBUG("{}", __FUNCTION__);
    if (!out_min_size || !out_max_size)
    {
        HERON_LOG_ERROR("{} out_min_size or out_max_size is null", __FUNCTION__);
        return NR_PLUGIN_RESULT_FAILURE;
    }
    ModelManager *p_mm = ModelManager::GetInstance();
    float min_size_meters, max_size_meters;
    p_mm->GetCanvasDiagonalSizeRangeMeters(&min_size_meters, &max_size_meters);
    *out_min_size = min_size_meters * p_mm->GetMarketingSizeFactor();
    *out_max_size = max_size_meters * p_mm->GetMarketingSizeFactor();
    HERON_LOG_DEBUG("{} min:{:.2f} inch max:{:.2f} inch min_meters:{} max_meters:{}",
                    __FUNCTION__, (*out_min_size) / METERS_PER_INCH, (*out_max_size) / METERS_PER_INCH, min_size_meters, max_size_meters);
    return NR_PLUGIN_RESULT_SUCCESS;
}

static NRPluginResult GetCanvasSizeLevelCount(NRPluginHandle handle, uint32_t *out_level_count)
{
    *out_level_count = ModelManager::GetInstance()->GetCanvasSizeLevelCount();
    HERON_LOG_DEBUG("{} {}", __FUNCTION__, *out_level_count);
    return NR_PLUGIN_RESULT_SUCCESS;
}

static NRPluginResult GetRoundedCanvasSizeLevel(NRPluginHandle handle, uint32_t *out_level)
{
    *out_level = ModelManager::GetInstance()->GetRoundedCanvasSizeLevel();
    HERON_LOG_DEBUG("{} {}", __FUNCTION__, *out_level);
    return NR_PLUGIN_RESULT_SUCCESS;
}

CallCounter s_hand_data_fps{"hand_data", 100, 0.0f, std::numeric_limits<float>::max()};
static NRPluginResult NotifyHandData(NRPluginHandle handle, uint64_t hmd_time_nanos, const NRHandData *hand_data, uint32_t hand_num)
{
    s_hand_data_fps.Update();
    std::vector<HandData> hand_datas;
    for (uint32_t i = 0; i < hand_num; i++)
    {
        if (DebugManager::GetInstance()->debug_log.hand_data_detail)
        {
            HERON_LOG_DEBUG("{} hand_num:{} hand_data[{}].hand_type:{} is_tracked:{} joint_count:{}", __FUNCTION__,
                            hand_num, i, hand_data[i].hand_type, hand_data[i].is_tracked, hand_data[i].hand_joint_count);
        }
        if (hand_data[i].is_tracked)
        {
            HandData hand_data_tmp;
            ConvertToHandData(hand_data_tmp, hand_data[i]);
            hand_datas.emplace_back(hand_data_tmp);
            if (DebugManager::GetInstance()->debug_log.hand_data_detail)
            {
                std::string hand_tag = hand_data[i].hand_type == NR_HAND_TYPE_LEFT ? "Left hand data" : "Right hand data";
                PrintObject(hand_tag, hand_data_tmp);
            }
        }
    }
    control::DisplayCtrl::GetInstance()->HandleHandData(hand_datas.data(), hand_num);
    return NR_PLUGIN_RESULT_SUCCESS;
}
