#include <heron/env/system_config.h>
#include <heron/env/version.h>
#include <heron/interface/include/public/nr_plugin_types.h>
#include <heron/util/log.h>
#include <heron/util/debug.h>

#include <framework/util/util.h>

using namespace framework::util;

/// interface
static NRPluginResult SetGlassesNetLogEnable(NRPluginHandle handle,
                                             ::NREnableValue enable_value,
                                             const char *ip, uint32_t ip_size, uint32_t port);
static NRPluginResult GetSystemStatus(NRPluginHandle handle,
                                      const char **sub_system_key, uint32_t *sub_system_key_size,
                                      const char **sub_system_value, uint32_t *sub_system_value_size);

/// global variables
static std::vector<void *> s_misc_provider{
    (void *)&SetGlassesNetLogEnable,
    (void *)&GetSystemStatus,
};

const void *GetMiscProvider(uint32_t &size) // size in bytes
{
    size = s_misc_provider.size() * sizeof(void *);
    return s_misc_provider.data();
}

NRPluginResult SetGlassesNetLogEnable(NRPluginHandle handle,
                                      ::NREnableValue enable_value,
                                      const char *ip, uint32_t ip_size, uint32_t port)
{

    HERON_LOG_DEBUG("{}", __FUNCTION__);
    int32_t log_level = heron::Logger::GetInstance()->GetLogLevel();
    if (enable_value == ::NR_ENABLE_VALUE_ENABLE)
    {
        heron::Logger::GetInstance()->GetLogger()->add_feature_tcp(
            std::string(ip, ip_size), port);
        if (heron::DebugManager::GetInstance()->no_sys_log_when_sending_tcp_log)
            heron::Logger::GetInstance()->GetLogger()->remove_feature_syslog();
    }
    else
    {
        heron::Logger::GetInstance()->GetLogger()->remove_all_feature();
    }
    heron::Logger::GetInstance()->SetLogLevel(log_level);

    return NR_PLUGIN_RESULT_SUCCESS;
}

static std::string plugin_name = "Flinger";
NRPluginResult GetSystemStatus(NRPluginHandle handle,
                               const char **sub_system_key, uint32_t *sub_system_key_size,
                               const char **sub_system_value, uint32_t *sub_system_value_size)
{
    HERON_LOG_DEBUG("{}", __FUNCTION__);
    *sub_system_key = plugin_name.data();
    *sub_system_key_size = plugin_name.size();
    *sub_system_value = HERON_VERSION_STRING;
    *sub_system_value_size = strlen(HERON_VERSION_STRING);
    return NR_PLUGIN_RESULT_SUCCESS;
}
