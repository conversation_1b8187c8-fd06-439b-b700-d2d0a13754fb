#include <heron/model/model.h>
#include <heron/model/permanent_config.h>
#include <heron/util/misc.h>
#include <heron/util/math_tools.h>
#include <heron/util/log.h>

using namespace heron;
using namespace heron::model;

// quad_base size is always the size at DEPTH_METERS_FOR_PUPIL_ADJUST
float SpaceScreenStatus::GetTargetFovFactorOnQuadBase() const
{
    return interpolated_size_factor.GetTarget() / GetTargetDepth() * DEPTH_METERS_FOR_PUPIL_ADJUST;
}

// XXX: only used for debug log print
float SpaceScreenStatus::GetTargetFovFactor() const
{
    return GetTargetFovFactorOnQuadBase() * interpolated_pupil_adjustment_factor.GetTarget();
}

void SpaceScreenStatus::GetTargetSizeMeters(Vector2f &out_size_meters) const
{
    Vector2f base_quad_size_meters = GetTargetPupilAdjustedBaseSizeMeters();
    out_size_meters.x() = base_quad_size_meters.x() * interpolated_size_factor.GetTarget();
    if (space_mode == SPACE_MODE_ULTRA_WIDE)
        out_size_meters.x() *= width_factor;
    out_size_meters.y() = base_quad_size_meters.y() * interpolated_size_factor.GetTarget();
}

void SpaceScreenStatus::GetTargetSizeMeters(Vector2f &out_size_meters, float width_factor) const
{
    Vector2f base_quad_size_meters = GetTargetPupilAdjustedBaseSizeMeters();
    out_size_meters.x() = base_quad_size_meters.x() * interpolated_size_factor.GetTarget() * width_factor;
    out_size_meters.y() = base_quad_size_meters.y() * interpolated_size_factor.GetTarget();
}

float SpaceScreenStatus::GetTargetDiagonalSizeMeters() const
{
    Vector2f size_meters;
    GetTargetSizeMeters(size_meters);
    return size_meters.norm();
}

float SpaceScreenStatus::GetTargetDiagonalSizeMeters(float width_factor) const
{
    Vector2f size_meters;
    GetTargetSizeMeters(size_meters, width_factor);
    return size_meters.norm();
}

// XXX: quad_base left:-1.5335758 right:1.5457242 top:0.8552945 bottom:-0.8768117
void SpaceScreenStatus::SetQuadBase(const Rectf &base)
{
    quad_base_size_meters = Vector2f(abs(base.right - base.left), abs(base.bottom - base.top));
    quad_base_center_meters = Vector2f((base.left + base.right) / 2.0f, (base.top + base.bottom) / 2.0f);
    interpolated_quad_center_meters.SetTarget(quad_base_center_meters, 0);
}
void SpaceScreenStatus::SetTargetSizeFactorOnQuadBase(float factor, uint32_t transition_ms)
{
    interpolated_size_factor.SetTarget(factor, transition_ms);
    interpolated_quad_center_meters.SetTarget(quad_base_center_meters * factor, transition_ms);
}
void SpaceScreenStatus::SetTargetDepth(float depth, uint32_t transition_ms)
{
    interpolated_depth.SetTarget(depth, transition_ms);
}
void SpaceScreenStatus::PupilAdjustment(float factor, uint32_t transition_ms)
{
    interpolated_pupil_adjustment_factor.SetTarget(factor, transition_ms);
    interpolated_quad_center_meters.SetTarget(quad_base_center_meters * factor, transition_ms);
}

// NormalQuad is center aligned
void SpaceScreenStatus::ApplyNormalQuad(float size_factor, float depth, uint32_t transition_ms)
{
    interpolated_quad_center_meters.SetTarget(quad_base_center_meters * size_factor, transition_ms);
    interpolated_size_factor.SetTarget(size_factor, transition_ms);
    interpolated_depth.SetTarget(depth, transition_ms);
}

// ThumbnailQuad is aligned to the edge of the base quad
void SpaceScreenStatus::ApplyThumbnailQuad(ThumbnailPositionType type, int margin_pixel, float size_factor, float depth, uint32_t transition_ms)
{
    Vector2f pupil_adjusted_base_quad_size_meters = GetTargetPupilAdjustedBaseSizeMeters();
    Vector2f target_thumbnail_size_meters = pupil_adjusted_base_quad_size_meters * size_factor;
    float margin_meters = (float)margin_pixel / (float)default_src_size_pixel.x() * pupil_adjusted_base_quad_size_meters.x();
    HERON_LOG_DEBUG("ApplyThumbnailQuad type:{} margin:{:.2f}m factor:{:.2f} depth:{:.2f}m", type, margin_meters, size_factor, depth);
    Vector2f pupil_adjusted_base_center_meters = quad_base_center_meters * interpolated_pupil_adjustment_factor.GetTarget();
    float left = pupil_adjusted_base_center_meters.x() - pupil_adjusted_base_quad_size_meters.x() / 2.0f;
    float right = pupil_adjusted_base_center_meters.x() + pupil_adjusted_base_quad_size_meters.x() / 2.0f;
    float top = pupil_adjusted_base_center_meters.y() + pupil_adjusted_base_quad_size_meters.y() / 2.0f;
    // float bottom = pupil_adjusted_base_center_meters.y() - pupil_adjusted_base_quad_size_meters.y() / 2.0f;
    Vector2f quad_center_meters;
    if (type == THUMBNAIL_POSE_TYPE_LEFT_TOP)
        quad_center_meters.x() = left + margin_meters + target_thumbnail_size_meters.x() / 2.0f;
    else
        quad_center_meters.x() = right - margin_meters - target_thumbnail_size_meters.x() / 2.0f;
    quad_center_meters.y() = top - margin_meters - target_thumbnail_size_meters.y() / 2.0f;
    interpolated_quad_center_meters.SetTarget(quad_center_meters, transition_ms);
    interpolated_size_factor.SetTarget(size_factor, transition_ms);
    interpolated_depth.SetTarget(depth, transition_ms);
}

void SpaceScreenStatus::Update()
{
    interpolated_quad_center_meters.Update();
    interpolated_pupil_adjustment_factor.Update();
    interpolated_size_factor.Update();
    interpolated_depth.Update();
    // UpdateQuadModelTransform(DOF_TYPE_ROTATION_Z, 0.1);
}

// considering ULTRA_WIDE and transition
void SpaceScreenStatus::GetCurrentSizeMeters(Vector2f &out_size_meters) const
{
    out_size_meters.x() = quad_base_size_meters.x() * interpolated_pupil_adjustment_factor.GetCurrent() * interpolated_size_factor.GetCurrent();
    if (space_mode == SPACE_MODE_ULTRA_WIDE)
        out_size_meters.x() *= width_factor;
    out_size_meters.y() = quad_base_size_meters.y() * interpolated_pupil_adjustment_factor.GetCurrent() * interpolated_size_factor.GetCurrent();
}

void SpaceScreenStatus::GetTransform(Transform &out_transform) const
{
    Vector2f quad_center_meters = interpolated_quad_center_meters.GetCurrent();
    if (space_mode == SPACE_MODE_THUMBNAIL || perception_type == PERCEPTION_TYPE_0DOF || perception_type == PERCEPTION_TYPE_EIS)
    {
        out_transform.rotation = Eigen::Quaternionf::Identity();
        out_transform.position = Vector3f(quad_center_meters.x(), quad_center_meters.y(), -interpolated_depth.GetCurrent());
    }
    else
    {
        out_transform.rotation = model.rotation;
        out_transform.position = model.position + Vector3f(quad_center_meters.x(), quad_center_meters.y(), -interpolated_depth.GetCurrent());
    }
}

void SpaceScreenStatus::GetModelTransform(Transform &out_transform) const
{
        out_transform.rotation = model.rotation;
        out_transform.position = model.position + Vector3f(0.0f, 0.0f, -interpolated_depth.GetTarget());
}

// fov is a calculated value with canvas size meters and canvas depth meters.
// We assume that canvas normal is parallel to the user's eye ray direction when calculating actual fov.
void SpaceScreenStatus::GetCurrentFovDegree(float &h_angle, float &v_angle) const
{
    Vector2f size_meters;
    GetCurrentSizeMeters(size_meters);
    v_angle = atan(size_meters.y() / 2 / GetCurrentDepth()) / M_PI * 180.0 * 2;
    if (space_mode == SPACE_MODE_ULTRA_WIDE)
        h_angle = size_meters.x() / GetCurrentDepth() / M_PI * 180.0;
    else
        h_angle = atan(size_meters.x() / 2 / GetCurrentDepth()) / M_PI * 180.0 * 2;
}