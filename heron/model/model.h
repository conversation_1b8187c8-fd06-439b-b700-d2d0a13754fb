#pragma once

#include <heron/util/types.h>
#include <heron/util/math_tools.h>
#include <heron/message/msg_types_twin.h>

#include <memory>

namespace heron::model
{
    const float DEPTH_METERS_FOR_PUPIL_ADJUST = 4.0f;

    class DisplayMetaData
    {
    public:
        uint32_t width;
        uint32_t height;
        float k_screen[9];
        DistortionInfo distortion_info;
    };

    class SpaceScreenStatus
    {
    public:
        struct Validation
        {
            bool first_frames_to_ignore = false;
            bool flushed_src_frame = true;
            bool bad_view = true;
            bool always_show = false;
            uint32_t InvalidFrame() const
            {
                uint32_t reason = 0 | flushed_src_frame;
                return reason;
            }
            uint32_t NotToPresent() const
            {
                if (always_show)
                    return 0;
                uint32_t reason = 0 | first_frames_to_ignore;
                reason <<= 1;
                reason |= bad_view;
                reason <<= 1;
                reason |= flushed_src_frame;
                return reason;
            }
        };
        SceneMode scene_mode = SCENE_MODE_SPACE_SCREEN;
        DpInputMode dp_input_mode = DP_INPUT_MODE_MONO;
        SpaceMode space_mode = SPACE_MODE_HOVER;
        LateStageReprojectionMode lsr_mode = WARP_MODE_PLANE;
        PerceptionType perception_type = PERCEPTION_TYPE_3DOF;
        bool pupil_adjust = false;
        Mat4f host_projection[DISPLAY_USAGE_COUNT];
        Validation validation;

    private:
        Interpolator<float> interpolated_depth;
        Interpolator<float> interpolated_size_factor;
        Interpolator<Vector2f> interpolated_quad_center_meters;
        Interpolator<float> interpolated_pupil_adjustment_factor;
        Vector2f quad_base_size_meters;
        Vector2f quad_base_center_meters;

    public:
        void AlwaysShow() { validation.always_show = true; };
        void SetQuadBase(const Rectf &base);
        void SetTargetSizeFactorOnQuadBase(float factor, uint32_t transition_ms = 0);
        void SetTargetDepth(float depth, uint32_t transition_ms = 0);
        void PupilAdjustment(float factor, uint32_t transition_ms = 0);
        void ApplyNormalQuad(float size_factor, float depth, uint32_t transition_ms = 0);
        void ApplyThumbnailQuad(ThumbnailPositionType type, int margin_pixel, float size_factor, float depth, uint32_t transition_ms = 0);
        float GetTargetFovFactorOnQuadBase() const;
        float GetTargetFovFactor() const;
        void GetCurrentFovDegree(float &h_angle, float &v_angle) const;
        void GetCurrentSizeMeters(Vector2f &out_size_meters) const;
        void GetTransform(Transform &out_transform) const;
        void GetModelTransform(Transform &out_transform) const;
        float GetTargetDiagonalSizeMeters() const;
        float GetTargetDiagonalSizeMeters(float width_factor) const;
        float GetCurrentDepth() const { return interpolated_depth.GetCurrent(); }
        float GetTargetDepth() const { return interpolated_depth.GetTarget(); }
        float GetTargetSizeFactorOnQuadBase() const { return interpolated_size_factor.GetTarget(); }
        Vector2f GetTargetPupilAdjustedBaseSizeMeters() const
        {
            return quad_base_size_meters * interpolated_pupil_adjustment_factor.GetTarget();
        }

        void Update();

    private:
        void GetTargetSizeMeters(Vector2f &out_size_meters) const;
        void GetTargetSizeMeters(Vector2f &out_size_meters, float width_factor) const;

        Vector2f GetCurrentPupilAdjustedBaseSizeMeters() const
        {
            return quad_base_size_meters * interpolated_pupil_adjustment_factor.GetCurrent();
        }

    private:
        Vector2i default_src_size_pixel;
        Vector2i current_src_size_pixel;
        float width_factor = 1.0f;

    public:
        void SetDefaultSrcSizePixel(Vector2i size_pixel) { default_src_size_pixel = size_pixel; }
        void SetCurrentSrcSizePixel(Vector2i size_pixel)
        {
            current_src_size_pixel = size_pixel;
            width_factor = (float)current_src_size_pixel.x() / default_src_size_pixel.x();
        }

    private:
        Transform model;

    public:
        void UpdateQuadModelTransform(const Transform &transform) { model = transform; }
    };

    class FrameInfo
    {
    public:
        FrameUsage usage{FrameUsage::FRAME_USAGE_ANY};
        void *nr_dp_frame{nullptr};
        DpFrameData dp_frame_data;
        DpFrameData *depth_shifted_frame_data;
        SpaceScreenStatus space_screen_status;
        bool gotten{false}; // means nr_dp_frame is populated by DpGetFrame(), not local generated
        FrameMetadataInternal metadata;
        Vector2f target_size_factor;
    };
    class OSDFrameInfo
    {
    public:
        uint32_t width;
        uint32_t height;
        FramebufferFormat format;
        uint32_t data_length;
        void *virtual_address{nullptr};
        uint64_t physical_address = 0;
    };
} // namespace heron::model

using SpaceScreenStatusPtr = std::shared_ptr<heron::model::SpaceScreenStatus>;