#pragma once

#include <heron/util/enums.h>
#include <heron/util/nr_types_twin.h>
#include <heron/message/msg_types_twin.h>

#include <warpcore/warp_types.h>
using namespace warp_core;

namespace heron
{
    struct FrameMetaDataHeader
    {
        // magic number用于让眼睛端确认dp是埋数据了
        uint32_t magic_number_high = 0x5AF48B7B;
        // 单位是字节
        int32_t len;
        uint32_t magic_number_low = 0xB2DCF8E6;
    };

    struct AspectRatio
    {
        uint32_t width;
        uint32_t height;
    };

    struct XR_VO_CALLBACK_INFO
    {
        uint64_t irq_timestamp_ns;
        XR_VO_IRQ_TYPE callback_type;
        bool need_reset;
    };

    struct DistortionVertexPair
    {
        float point_x;
        float point_y;
        float distort_point_x;
        float distort_point_y;

        DistortionVertexPair()
        {
            point_x = point_y = distort_point_x = distort_point_y = .0f;
        }
        DistortionVertexPair(float pt_x, float pt_y, float dis_pt_x, float dis_pt_y) : point_x(pt_x), point_y(pt_y), distort_point_x(dis_pt_x), distort_point_y(dis_pt_y)
        {
        }
    };

    struct DistortionInfo
    {
        // DistortionType distortion_type;
        uint32_t num_rows;
        uint32_t num_columns;
        // std::vector<DistortionVertexPair> distort_mesh_pts;
        std::vector<float> mesh_for_gdc;
        std::vector<float> identity_mesh_for_gdc;
    };

    struct FrameMetadataTiming
    {
        uint64_t start_render_ns;
        uint64_t submit_ns;
        uint64_t data_device_ns;
        uint64_t data_recv_ns;
        uint64_t sys_pose_ns;
        uint64_t device_pose_ns; // host predicted pose time
        uint64_t frame_apply_ns;
        uint64_t mid_display_ns;
        uint64_t embedded_device_ns;
        uint64_t dp_rx_done_ns;
        uint64_t frame_parsed_ns;
        uint64_t vsync_generated_ns;
        uint64_t vsync_sent_ns;
        uint64_t embedded_data_parsed_ns;
    };

    struct FrameMetadataInternal
    {
        uint64_t frame_number;
        uint64_t pose_guid_high;
        uint64_t pose_guid_low;
        LateStageReprojectionMode lsr_mode;
        FrameMetadataTiming timing;
        std::vector<BufferMetadataTwin> metadata_vec;
    };

    struct DpVideoPipelineParam
    {
        uint32_t SRC_WIDTH = SRC_FRAME_WIDTH_DEFAULT;
        uint32_t SRC_HEIGHT = SRC_FRAME_HEIGHT_DEFAULT;
        uint32_t SCREEN_WIDTH = SCREEN_WIDTH_DEFAULT;
        uint32_t SCREEN_HEIGHT = SCREEN_HEIGHT_DEFAULT;
        DpVideoPipelineParam(uint32_t src_width, uint32_t src_height,
                             uint32_t screen_width, uint32_t screen_height)
            : SRC_WIDTH(src_width), SRC_HEIGHT(src_height),
              SCREEN_WIDTH(screen_width), SCREEN_HEIGHT(screen_height) {}
    };
    struct WarpTiming
    {
        uint32_t warp_delay_block_cnt = 4;
        uint32_t overwrite_block_cnt = 0;
        uint32_t warp_block_cnt_at_each_callback = 8;
        uint32_t get_frame_before_special = 0;
        WarpTiming() {}
        WarpTiming(uint32_t warp_delay_block_cnt,
                   uint32_t overwrite_block_cnt,
                   uint32_t warp_block_cnt_at_each_callback,
                   uint32_t get_frame_before_special)
            : warp_delay_block_cnt(warp_delay_block_cnt),
              overwrite_block_cnt(overwrite_block_cnt),
              warp_block_cnt_at_each_callback(warp_block_cnt_at_each_callback),
              get_frame_before_special(get_frame_before_special) {}
    };

    struct Image
    {
        uint32_t width;
        uint32_t height;
        FramebufferFormat pixel_format;
        uint32_t strides[4];
        char *data[4];
    };

} // namespace heron
