#pragma once

#include <stdint.h>
#include <atomic>
#include <vector>
#include <mutex>

#define INVALID_INDEX -1

namespace heron
{
    template <typename Value>
    class DoubleBuffer
    {
    public:
        DoubleBuffer() {}

    public:
        Value *GetReadableBuffer()
        {
            buffer_mutex_.lock();
            return curr_readable_buffer_;
        }
        void DoneReadBuffer()
        {
            buffer_mutex_.unlock();
        }
        void Submit(Value *value)
        {
            buffer_mutex_.lock();
            curr_readable_buffer_ = value;
            buffer_mutex_.unlock();
        }

    private:
        std::mutex buffer_mutex_;
        Value *curr_readable_buffer_ = nullptr;
    };

    template <typename Value>
    class RingBufferHoldNextR
    {
    public:
        typedef typename std::vector<Value> ValueVec;

    public:
        RingBufferHoldNextR(int32_t buffer_size)
            : buffer_(buffer_size),
              buffer_size_(buffer_size),
              cur_read_index_(-1),
              next_read_index_(-1),
              cur_write_index_(0),
              last_write_index_(0) {}

        RingBufferHoldNextR(RingBufferHoldNextR &&other)
        {
            buffer_ = std::move(other.buffer_);
            buffer_size_ = other.buffer_size_;
            cur_read_index_ = other.cur_read_index_.load();
            next_read_index_ = other.next_read_index_.load();
            cur_write_index_ = other.cur_write_index_.load();
            last_write_index_ = other.last_write_index_.load();
        }

        /***********************************
         *
         *		pay attention:
         *		1. getWritable and doneWrite is in one thread
         *		2. getReadable and doneRead is in another thread
         *
         **********************************/
        Value *GetWritableBuffer()
        {
            for (int32_t i = 0; i < buffer_size_; ++i)
            {
                cur_write_index_ = last_write_index_ + i;
                int64_t read_index = cur_read_index_ % buffer_size_;
                int64_t next_read_index = next_read_index_ % buffer_size_;
                int64_t write_index = cur_write_index_ % buffer_size_;
                if (write_index != read_index && write_index != next_read_index)
                {
                    return &buffer_[write_index];
                }
            }
            return nullptr;
        }
        void DoneWriteBuffer()
        {
            last_write_index_ = cur_write_index_.load();
        }

        Value *GetReadableBuffer()
        {
            int32_t index = 0;
            if (cur_read_index_ == -1)
            {
                cur_read_index_ = last_write_index_.load();
                index = cur_read_index_ % buffer_size_;
                return &buffer_[index];
            }
            if (next_read_index_ != -1)
                return nullptr;
            next_read_index_ = last_write_index_.load();
            index = next_read_index_ % buffer_size_;
            return &buffer_[index];
        }
        void DoneReadBuffer()
        {
            cur_read_index_ = next_read_index_.load();
            next_read_index_ = -1;
        }
        int32_t GetSize() const { return buffer_size_; }
        Value *GetBufferByIndex(int32_t idx)
        {
            if (idx >= buffer_size_)
                return nullptr;
            return &buffer_[idx];
        }

    protected:
        ValueVec buffer_;
        int32_t buffer_size_;
        std::atomic<int64_t> cur_read_index_;
        std::atomic<int64_t> next_read_index_;
        std::atomic<int64_t> cur_write_index_;
        std::atomic<int64_t> last_write_index_;
    };

    template <typename Value>
    class RingBufferNoProtection
    {
    public:
        typedef typename std::vector<Value> ValueVec;

    public:
        RingBufferNoProtection(int32_t buffer_size)
            : buffer_(buffer_size),
              buffer_size_(buffer_size) {}

        RingBufferNoProtection(RingBufferNoProtection &&other)
        {
            buffer_ = std::move(other.buffer_);
            buffer_size_ = other.buffer_size_;
        }

        /***********************************
         *
         *		pay attention:
         *		1. getWritable and doneWrite is in one thread
         *		2. getReadable and doneRead is in another thread
         *
         **********************************/
        Value *GetWritableBuffer()
        {
            return &buffer_[0];
        }
        void DoneWriteBuffer()
        {
        }

        Value *GetReadableBuffer()
        {
            return &buffer_[0];
        }
        void DoneReadBuffer()
        {
        }
        int32_t GetSize() const { return buffer_size_; }

    protected:
        ValueVec buffer_;
        int32_t buffer_size_;
    };

    template <typename Value>
    class RingBuffer
    {
    public:
        typedef typename std::vector<Value> ValueVec;

    public:
        RingBuffer(int32_t buffer_size)
            : buffer_(buffer_size),
              buffer_size_(buffer_size),
              cur_buffer_index_(INVALID_INDEX) {}

        RingBuffer(RingBuffer &&other)
        {
            buffer_ = std::move(other.buffer_);
            buffer_size_ = other.buffer_size_;
            cur_buffer_index_ = other.cur_buffer_index_.load();
        }

        bool IsIndexValid(int32_t index) const
        {
            return index != INVALID_INDEX;
        }

        /***********************************
         *
         *		pay attention:
         *		1. getWritable and doneWrite is in one thread
         *		2. getReadable and get is in another thread
         *		assumption:
         *		1. always read new one
         *
         **********************************/
        Value *GetWritableBuffer()
        {
            int32_t index = cur_buffer_index_ + 1;
            if (index == buffer_size_)
                index = 0;
            return &buffer_[index];
        }
        void DoneWriteBuffer()
        {
            if (++cur_buffer_index_ == buffer_size_)
                cur_buffer_index_ = 0;
        }
        int32_t GetReadableBufferIndex() const { return cur_buffer_index_; }
        Value *GetBuffer(int32_t index)
        {
            if (index == buffer_size_)
                return &buffer_[0];
            return &buffer_[index];
        }
        int32_t GetSize() const { return buffer_size_; }

    protected:
        ValueVec buffer_;
        int32_t buffer_size_;
        std::atomic_int cur_buffer_index_;
    };

    template <typename T>
    class BoundedBlockingQueue
    {
    public:
        explicit BoundedBlockingQueue(size_t max_size)
            : max_size_(max_size)
        {
            if (max_size == 0)
            {
                throw std::invalid_argument("Queue size must be > 0");
            }
        }

        // 入队（阻塞直到队列有空位）
        void Push(const T &item)
        {
            {
                std::unique_lock<std::mutex> lock(mutex_);
                not_full_.wait(lock, [this]()
                               { return queue_.size() < max_size_ || stop_; });
                if (stop_)
                {
                    throw std::runtime_error("Queue is stopped");
                }
                queue_.push(item);
            }
            not_empty_.notify_one(); // 通知消费者
        }

        // 出队（阻塞直到队列非空）
        T Pop()
        {
            T item;
            {
                std::unique_lock<std::mutex> lock(mutex_);
                not_empty_.wait(lock, [this]()
                                { return !queue_.empty() || stop_; });
                if (stop_ && queue_.empty())
                {
                    throw std::runtime_error("Queue is stopped and empty");
                }
                item = queue_.front();
                queue_.pop();
            }
            not_full_.notify_one(); // 通知生产者
            return item;
        }

        // 停止队列（唤醒所有等待线程）
        void Stop()
        {
            {
                std::lock_guard<std::mutex> lock(mutex_);
                stop_ = true;
            }
            not_empty_.notify_all(); // 唤醒所有消费者
            not_full_.notify_all();  // 唤醒所有生产者
        }

        size_t Size() const
        {
            std::lock_guard<std::mutex> lock(mutex_);
            return queue_.size();
        }

        bool Empty() const
        {
            std::lock_guard<std::mutex> lock(mutex_);
            return queue_.empty();
        }

        bool Full() const
        {
            std::lock_guard<std::mutex> lock(mutex_);
            return queue_.size() >= max_size_;
        }

    private:
        std::queue<T> queue_;
        mutable std::mutex mutex_;
        std::condition_variable not_empty_; // 队列非空时通知消费者
        std::condition_variable not_full_;  // 队列未满时通知生产者
        size_t max_size_;
        bool stop_ = false; // 队列是否已停止
    };

} // namespace heron