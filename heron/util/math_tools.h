#pragma once
#include <heron/util/types.h>
#include <heron/util/debug.h>
#include <heron/util/misc.h>

#define DIV_CEIL(x, y) (((x) + (y) - 1) / (y))
#define ALIGN_BACK(x, a) ((a) * (((x + a - 1) / (a))))
#define ALIGN128(_x) (((_x) + 0x7f) & ~0x7f)

namespace heron
{
    void IsLookingAt(bool &inner, bool &outer, float fov_up, float fov_down, float fov_sides,
                     const Transform &head_transform, const Transform &recenter_transform,
                     const Transform &quad_transform, const Transform &recentered_quad_transform);
    bool PointInRect(const Eigen::Vector2f &pt, const Vector2i &target_rect_size);
    void WorldToHomogeneous(const Vector3f world_3d_coord, const Transform &head_pose,
                            const Mat4f &projection, Vector4f &homogeneous);
    bool CanvasProjectionOutOfScreen(const Vector4f &tl, const Vector4f &tr, const Vector4f &bl, const Vector4f &br, const Vector2i &screen_size_pixel,
                                     Vector2f &tl_screen, Vector2f &tr_screen, Vector2f &bl_screen, Vector2f &br_screen, bool &tl_in, bool &tr_in, bool &bl_in, bool &br_in);
    bool GetCanvasProjectionSizeFactor(const Vector4f &tl, const Vector4f &tr, const Vector4f &bl, const Vector4f &br,
                                       const Vector2i &screen_size_pixel, Vector2f &size_factor, bool &h_factor_valid, bool &v_factor_valid);
    void IsBadView6dofTest(const Transform &quad_transform, const Transform &head_pose, float quad_size_meters, bool &very_bad, bool &bad);
    void IsInCylinder(const Transform &recenter_transform, const Transform &head_pose, float radius, bool &inner, bool &outer);
    std::pair<Vector2f, Vector2f> ComputeBoundingBox(const std::vector<Vector2f> &points);
    float ComputeOverlapArea(const std::pair<Vector2f, Vector2f> &box1,
                             const std::pair<Vector2f, Vector2f> &box2);
    Eigen::Quaternionf QuaternionFromEuler(float ai, float aj, float ak, const std::string &axes);
    std::tuple<float, float, float> EulerFromMatrix(const Eigen::Matrix3f &matrix, const std::string &axes);

    inline float EaseOutExpo(float x)
    {
        float t = x > 1.0f ? 1.0f : 1.0f - std::pow(2, -10 * x);
        return t;
    }
    template <typename T>
    class Interpolator
    {
    public:
        void SetTarget(const T &new_taget, uint32_t duration_ms)
        {
            start_val_ = curr_val_;
            target_val_ = new_taget;
            transitioning_ = true;
            transition_start_ns_ = GetTimeNano();
            transition_duration_ns_ = duration_ms * 1000000;
        }
        T GetTarget() const { return target_val_; }
        T GetCurrent() const { return curr_val_; }
        void Update()
        {
            if (!transitioning_)
                return;
            uint64_t now = GetTimeNano();
            uint64_t elapsed = now - transition_start_ns_;
            if (elapsed >= transition_duration_ns_)
            {
                curr_val_ = target_val_;
                transitioning_ = false;
                return;
            }
            curr_val_ = start_val_ + (target_val_ - start_val_) * EaseOutExpo((float)(elapsed / (double)transition_duration_ns_));
        }

    private:
        T curr_val_;
        T start_val_;
        T target_val_;

        bool transitioning_ = false;
        uint64_t transition_start_ns_;
        uint64_t transition_duration_ns_;
    };
}
