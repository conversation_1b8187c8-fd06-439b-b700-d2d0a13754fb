#include <heron/util/warp.h>
#include <heron/util/math_tools.h>

using namespace heron;

namespace heron
{
    namespace warp
    {
        static bool CheckObject(std::string key_msg, const Vector3f &candidate, const Vector3f &expected, float epsilion)
        {
            for (int i = 0; i < 3; i++)
            {
                if (abs(candidate(i) - expected(i)) > epsilion)
                {
                    HERON_LOG_ERROR("{} check error. candidate[{}]={} is not expected[{}]={}. diff larger than thresh: {}",
                                    key_msg, i, candidate(i), i, expected(i), epsilion);
                    return false;
                }
            }
            return true;
        }
        static bool CheckObject(std::string key_msg, const Quatf &candidate, const Quatf &expected, float epsilion)
        {
            if (abs(candidate.x() - expected.x()) > epsilion)
            {
                HERON_LOG_ERROR("{} check error. candidate.x={} is not expected.x={}. diff larger than thresh: {}",
                                key_msg, candidate.x(), expected.x(), epsilion);
                return false;
            }
            if (abs(candidate.y() - expected.y()) > epsilion)
            {
                HERON_LOG_ERROR("{} check error. candidate.y={} is not expected.y={}. diff larger than thresh: {}",
                                key_msg, candidate.y(), expected.y(), epsilion);
                return false;
            }
            if (abs(candidate.z() - expected.z()) > epsilion)
            {
                HERON_LOG_ERROR("{} check error. candidate.z={} is not expected.z={}. diff larger than thresh: {}",
                                key_msg, candidate.z(), expected.z(), epsilion);
                return false;
            }
            if (abs(candidate.w() - expected.w()) > epsilion)
            {
                HERON_LOG_ERROR("{} check error. candidate.w={} is not expected.w={}. diff larger than thresh: {}",
                                key_msg, candidate.w(), expected.w(), epsilion);
                return false;
            }
            return true;
        }

        bool CheckObject(std::string key_msg, const Mat3f &candidate, const Mat3f &expected, float epsilion)
        {
            for (int i = 0; i < 3; i++)
            {
                for (int j = 0; j < 3; j++)
                {
                    if (abs(candidate(i, j) - expected(i, j)) > epsilion)
                    {
                        HERON_LOG_ERROR("{} check error. candidate({}, {})={} is not expected({}, {})={}. diff larger than thresh: {}",
                                        key_msg, i, j, candidate(i, j), i, j, expected(i, j), epsilion);
                        return false;
                    }
                }
            }
            HERON_LOG_INFO("{} PASS! thresh: {}", key_msg, epsilion);
            return true;
        }

        bool CheckObject(std::string key_msg, const Transform &candidate, const Transform &expected, float epsilion)
        {
            if (!CheckObject(key_msg + "[position]", candidate.position, expected.position, epsilion))
                return false;
            if (!CheckObject(key_msg + "[rotation]", candidate.rotation, expected.rotation, epsilion))
                return false;
            HERON_LOG_INFO("{} PASS! thresh: {}", key_msg, epsilion);
            return true;
        }


        void CalcRecenterTransform(const Transform &head_transform, Transform &recenter_transform)
        {
            Mat3f head_rotation = head_transform.rotation.toRotationMatrix();
            std::tuple<float, float, float> euler_ryxz = EulerFromMatrix(head_rotation, "ryxz");
            HERON_LOG_DEBUG("recenter ryxz_euler: {:.4f},{:.4f},{:.4f}", std::get<0>(euler_ryxz), std::get<1>(euler_ryxz), std::get<2>(euler_ryxz));
            float pitch = std::get<1>(euler_ryxz) * (180.0f / M_PI);
            float roll = std::get<2>(euler_ryxz) * (180.0f / M_PI);
            if (abs(pitch) < 20 && abs(roll) < 30)
                roll = 0;
            recenter_transform.rotation = QuaternionFromEuler(std::get<0>(euler_ryxz), pitch * M_PI / 180.0f, roll * M_PI / 180.0f, "ryxz");
            recenter_transform.position = head_transform.position;
        }

    } // namespace warp
} // namespace heron