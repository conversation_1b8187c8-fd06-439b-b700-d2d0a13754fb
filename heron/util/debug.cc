#include <heron/util/debug.h>
#include <heron/util/config.h>
#include <heron/util/log.h>
#include <heron/util/misc.h>

#include <framework/util/json.h>

#include <thread>
#include <mutex>
#include <condition_variable>
#include <vector>
#include <queue>
#include <fstream>
#include <fcntl.h>    // For O_SYNC, O_RDWR, and other file control options
#include <sys/mman.h> // For mmap and related memory management functions

#define PARSE_JSON_FILED(root, name, var, type)    \
    if (root.isMember(#name))                      \
    {                                              \
        var = root[#name].as##type();              \
        HERON_LOG_DEBUG(#name " parsed: {}", var); \
    }

namespace heron
{
    static bool ParseDebugLog(const Json::Value &root, DebugManager::DebugLog &config)
    {
        PARSE_JSON_FILED(root, suitable_src_frame_size, config.suitable_src_frame_size, Bool);
        PARSE_JSON_FILED(root, focus_judger, config.focus_judger, Bool);
        PARSE_JSON_FILED(root, warp_job_block_id, config.warp_job_block_id, Bool);
        PARSE_JSON_FILED(root, embedded_metadata, config.embedded_metadata, Bool);
        PARSE_JSON_FILED(root, populated_metadata, config.populated_metadata, Bool);
        PARSE_JSON_FILED(root, msg_metadata, config.msg_metadata, Bool);
        PARSE_JSON_FILED(root, timing_detail, config.timing_detail, Bool);
        PARSE_JSON_FILED(root, timing_verbose, config.timing_verbose, Bool);
        PARSE_JSON_FILED(root, frame_data_buffer_status, config.frame_data_buffer_status, Bool);
        PARSE_JSON_FILED(root, dp_frame_data_detail, config.dp_frame_data_detail, Bool);
        PARSE_JSON_FILED(root, gdc_configured_frame_data_detail, config.gdc_configured_frame_data_detail, Bool);
        PARSE_JSON_FILED(root, quad_status_interpolation, config.quad_status_interpolation, Bool);
        PARSE_JSON_FILED(root, target_block_id, config.target_block_id, Bool);
        PARSE_JSON_FILED(root, head_pose, config.head_pose, Bool);
        PARSE_JSON_FILED(root, osd_submit, config.osd_submit, Bool);
        PARSE_JSON_FILED(root, hand_data_detail, config.hand_data_detail, Bool);
        return true;
    }

    static bool ParseGDCDebugConfig(const Json::Value &root, DebugManager::GDCDebugConfig &config)
    {
        PARSE_JSON_FILED(root, use_sram, config.use_sram, Bool);
        if (root.isMember("ext_padding"))
        {
            const Json::Value &padding = root["ext_padding"];
            config.padding_value_c0 = padding["c0"].asInt();
            config.padding_value_c1 = padding["c1"].asInt();
            config.padding_value_c2 = padding["c2"].asInt();
        }
        PARSE_JSON_FILED(root, start_mode, config.start_mode, Int);
        PARSE_JSON_FILED(root, warp_mode, config.warp_mode, Int);
        PARSE_JSON_FILED(root, warp_flush_cnt, config.warp_flush_cnt, Int);
        PARSE_JSON_FILED(root, mesh_mode, config.mesh_mode, Int);
        PARSE_JSON_FILED(root, use_identity_mesh, config.use_identity_mesh, Bool);
        PARSE_JSON_FILED(root, weight_mode, config.weight_mode, Int);
        return true;
    }

    static bool ParseTimingConfig(const Json::Value &root, WarpTiming &timing)
    {
        PARSE_JSON_FILED(root, warp_delay_block_cnt, timing.warp_delay_block_cnt, UInt);
        if (root.isMember("overwrite_block_cnt"))
        {
            timing.overwrite_block_cnt = root["overwrite_block_cnt"].asUInt() > 4 ? 4 : root["overwrite_block_cnt"].asUInt();
        }
        if (root.isMember("warp_block_cnt_at_each_callback"))
        {
            timing.warp_block_cnt_at_each_callback = root["warp_block_cnt_at_each_callback"].asUInt();
            if (timing.warp_block_cnt_at_each_callback - timing.overwrite_block_cnt < 4)
                timing.warp_block_cnt_at_each_callback = timing.overwrite_block_cnt + 4;
            if (timing.warp_block_cnt_at_each_callback > 12)
                timing.warp_block_cnt_at_each_callback = 12;
        }
        PARSE_JSON_FILED(root, get_frame_before_special, timing.get_frame_before_special, UInt);
        return true;
    }
    static bool ParseDumpDpInputConfig(const Json::Value &root, DebugManager::DumpDpInputConfig &dump_config)
    {
        PARSE_JSON_FILED(root, dump_dp_src_frame, dump_config.dump_dp_src_frame, Bool);
        PARSE_JSON_FILED(root, dump_whole_frame_interval, dump_config.dump_whole_frame_interval, UInt);
        PARSE_JSON_FILED(root, dump_first_lines, dump_config.dump_first_lines, Bool);
        PARSE_JSON_FILED(root, dump_first_lines_frame_interval, dump_config.dump_first_lines_frame_interval, UInt);
        PARSE_JSON_FILED(root, line_count, dump_config.line_count, UInt);
        PARSE_JSON_FILED(root, width, dump_config.width, UInt);
        return true;
    }
    static bool ParseDpVideoPipelineParam(const Json::Value &root, DpVideoPipelineParam &param)
    {
        PARSE_JSON_FILED(root, screen_width, param.SCREEN_WIDTH, UInt);
        PARSE_JSON_FILED(root, screen_height, param.SCREEN_HEIGHT, UInt);
        PARSE_JSON_FILED(root, src_width, param.SRC_WIDTH, UInt);
        PARSE_JSON_FILED(root, src_height, param.SRC_HEIGHT, UInt);
        return true;
    }

    static void ParseATWProfiler(const Json::Value &root)
    {
        int64_t line_start = 0;
        int64_t line_step = 0;
        uint32_t bar_count = 0;
        uint64_t duration_ns = 0;
        DebugManager *p_dm = DebugManager::GetInstance();
        do
        {
            if (root.isMember("line_start"))
                line_start = root["line_start"].asInt64();
            else
                break;
            if (root.isMember("line_step"))
                line_step = root["line_step"].asInt64();
            else
                break;
            if (root.isMember("bar_count"))
                bar_count = root["bar_count"].asUInt();
            else
                break;
            if (root.isMember("duration_seconds"))
                duration_ns = root["duration_seconds"].asUInt64() * OS_NS_PER_SEC;
            else
                break;
            p_dm->profile_atw = true;
        } while (false);
        if (!p_dm->profile_atw)
        {
            HERON_LOG_ERROR("ATWProfiler config error: {} {} {} {}",
                            line_start, line_step, bar_count, duration_ns);
        }
        else
        {
            p_dm->atw_line_diff =
                Distribution("atw_line_diff", line_start, line_step, bar_count, true, duration_ns);
        }
    }
    bool ParseGlobalConfig(const char *config, uint32_t size)
    {
        if (size == 0)
            return true;
        Json::Value root;
        Json::CharReaderBuilder json_builder;
        json_builder["collectComments"] = false;
        JSONCPP_STRING json_errs;
        std::istringstream json_stream(std::string(config, size));
        DebugManager *p_dm = DebugManager::GetInstance();
        if (!parseFromStream(json_builder, json_stream, &root, &json_errs))
        {
            HERON_LOG_ERROR("Parse global config sdk_global error, json_errs = {}", json_errs.c_str());
            return false;
        }
        if (!root.isMember("flinger"))
            return true;

        const Json::Value &flinger = root["flinger"];
        if (flinger.isMember("log_level"))
        {
            uint32_t log_level = atoi(flinger["log_level"].asString().c_str());
            heron::Logger::GetInstance()->SetLogLevel(log_level);
            p_dm->log_all_level = log_level == 1;
        }
        if (flinger.isMember("debug_log"))
        {
            HERON_LOG_DEBUG("parsing debug_log");
            const Json::Value &debug_log = flinger["debug_log"];
            ParseDebugLog(debug_log, p_dm->debug_log);
        }
        if (flinger.isMember("dp_video_pipeline_param"))
        {
            HERON_LOG_DEBUG("parsing DpVideoPipelineParam");
            p_dm->use_arbitrary_dp_video_pipeline_param = true;
            const Json::Value &params = flinger["dp_video_pipeline_param"];
            ParseDpVideoPipelineParam(params, p_dm->dp_video_pipeline_param);
        }
        PARSE_JSON_FILED(flinger, no_sys_log_when_sending_tcp_log, p_dm->no_sys_log_when_sending_tcp_log, Bool);
        PARSE_JSON_FILED(flinger, hide_metadata_lines, p_dm->hide_metadata_lines, Bool);
        PARSE_JSON_FILED(flinger, force_start_dp_render, p_dm->force_start_dp_render, Bool);
        PARSE_JSON_FILED(flinger, dp_frame_info_count, p_dm->dp_frame_info_count, UInt);
        PARSE_JSON_FILED(flinger, arbitrary_vo_fps, p_dm->arbitrary_vo_fps, Bool);
        PARSE_JSON_FILED(flinger, default_vo_fps, p_dm->default_vo_fps, Float);
        PARSE_JSON_FILED(flinger, lines_64_enable, p_dm->lines_64_enable, Bool);
        PARSE_JSON_FILED(flinger, init_line_cnt, p_dm->init_line_cnt, UInt);
        PARSE_JSON_FILED(flinger, sleep_us_after_dp_rx_done, p_dm->sleep_us_after_dp_rx_done, UInt);
        PARSE_JSON_FILED(flinger, modify_dp_src, p_dm->modify_dp_src, Bool);
        PARSE_JSON_FILED(flinger, ignore_space_status_validation, p_dm->ignore_space_status_validation, Bool);
        PARSE_JSON_FILED(flinger, no_update_matrix_buffer, p_dm->no_update_matrix_buffer, Bool);
        PARSE_JSON_FILED(flinger, mesh_only_warp, p_dm->mesh_only_warp, Bool);
        PARSE_JSON_FILED(flinger, mmz_cached_for_warp_matrices, p_dm->mmz_cached_for_warp_matrices, Bool);
        PARSE_JSON_FILED(flinger, pupil_adjust_mask, p_dm->pupil_adjust_mask, Bool);
        PARSE_JSON_FILED(flinger, pupil_adjust_component_pose, p_dm->pupil_adjust_component_pose, Bool);
        PARSE_JSON_FILED(flinger, use_async_warp, p_dm->use_async_warp, Bool);
        PARSE_JSON_FILED(flinger, use_host_ptw_warp, p_dm->use_host_ptw_warp, Bool);
        PARSE_JSON_FILED(flinger, only_use_left_vo_callback, p_dm->only_use_left_vo_callback, Bool);
        if (flinger.isMember("target_src_size_pixel"))
        {
            p_dm->arbitrary_src_size_pixel = true;
            const Json::Value &target_src_size_pixel = flinger["target_src_size_pixel"];
            p_dm->target_src_size_pixel.x() = target_src_size_pixel[0].asInt();
            p_dm->target_src_size_pixel.y() = target_src_size_pixel[1].asInt();
            HERON_LOG_DEBUG("forcely set target_src_size_pixel to {}x{}", p_dm->target_src_size_pixel.x(), p_dm->target_src_size_pixel.y());
        }
        PARSE_JSON_FILED(flinger, disable_warp_at_0DOF, p_dm->disable_warp_at_0DOF, Bool);
        PARSE_JSON_FILED(flinger, check_underflow, p_dm->check_underflow, Bool);
        PARSE_JSON_FILED(flinger, ignore_linebuffer_reset, p_dm->ignore_linebuffer_reset, Bool);
        if (flinger.isMember("debug_warp_matrix_file_path"))
        {
            p_dm->debug_warp_matrix_file_path = flinger["debug_warp_matrix_file_path"].asString();
            // Open the file in binary mode
            std::ifstream file(p_dm->debug_warp_matrix_file_path.c_str(), std::ios::binary);
            if (!file)
            {
                HERON_LOG_WARN("Could not open the file: {}", p_dm->debug_warp_matrix_file_path);
            }
            else
            {
                // Determine the size of the file
                file.seekg(0, std::ios::end);
                std::streamsize size = file.tellg();
                file.seekg(0, std::ios::beg);
                // Read the file content into the buffer
                if (size != 35 * 9 * sizeof(float))
                {
                    HERON_LOG_WARN("debugwarp_matrices data size error, get {}", size);
                }
                else
                {
                    if (!file.read(p_dm->debug_warp_matrices.data(), size))
                    {
                        HERON_LOG_WARN("Error reading the file! {}", p_dm->debug_warp_matrix_file_path);
                    }
                    else
                    {
                        HERON_LOG_INFO("load warp_matrices data from {} success of size: {}", p_dm->debug_warp_matrix_file_path, size);
                    }
                }
                file.close();
            }
        }
        PARSE_JSON_FILED(flinger, pose_timestamp_offset_ns, p_dm->pose_timestamp_offset_ns, Int64);
        if (flinger.isMember("warp_timing"))
        {
            const Json::Value &timing = flinger["warp_timing"];
            ParseTimingConfig(timing, p_dm->normal_timing);
        }
        if (flinger.isMember("ultra_wide_timing"))
        {
            const Json::Value &timing = flinger["ultra_wide_timing"];
            ParseTimingConfig(timing, p_dm->ultra_wide_timing);
        }
        if (flinger.isMember("with_nebula_timing"))
        {
            const Json::Value &timing = flinger["with_nebula_timing"];
            ParseTimingConfig(timing, p_dm->with_nebula_timing);
        }
        PARSE_JSON_FILED(flinger, print_frame_interval, p_dm->print_frame_interval, UInt);
        PARSE_JSON_FILED(flinger, print_interrupt_interval, p_dm->print_interrupt_interval, UInt);
        PARSE_JSON_FILED(flinger, profile_callback_interval, p_dm->profile_callback_interval, Bool);
        if (flinger.isMember("profile_atw"))
        {
            const Json::Value &atw_profiler_conf = flinger["profile_atw"];
            ParseATWProfiler(atw_profiler_conf);
        }

        PARSE_JSON_FILED(flinger, immediate_return_on_vo_callback, p_dm->immediate_return_on_vo_callback, Bool);
        PARSE_JSON_FILED(flinger, dp_rx_single_buffer, p_dm->dp_rx_single_buffer, Bool);
        PARSE_JSON_FILED(flinger, force_dp_rx_no_single_buffer, p_dm->force_dp_rx_no_single_buffer, Bool);

        if (flinger.isMember("gdc_left"))
        {
            HERON_LOG_DEBUG("parsing GDCDebugConfig for left");
            const Json::Value &gdc_left = flinger["gdc_left"];
            if (!ParseGDCDebugConfig(gdc_left, p_dm->gdc_configs[DISPLAY_USAGE_LEFT]))
                return false;
        }
        if (flinger.isMember("gdc_right"))
        {
            HERON_LOG_DEBUG("parsing GDCDebugConfig for right");
            const Json::Value &gdc_right = flinger["gdc_right"];
            if (!ParseGDCDebugConfig(gdc_right, p_dm->gdc_configs[DISPLAY_USAGE_RIGHT]))
                return false;
        }

        if (flinger.isMember("bypass_mode"))
        {
            p_dm->bypass_mode = flinger["bypass_mode"].asBool();
            if (p_dm->bypass_mode)
            {
                HERON_LOG_INFO("bypass_mode enabled");
                p_dm->gdc_configs[DISPLAY_USAGE_LEFT].mesh_mode = 3;
                p_dm->gdc_configs[DISPLAY_USAGE_RIGHT].mesh_mode = 3;
            }
        }
        if (flinger.isMember("dump_dp_input_config"))
        {
            HERON_LOG_DEBUG("parsing DumpDpInputConfig");
            const Json::Value &dump_config = flinger["dump_dp_input_config"];
            if (!ParseDumpDpInputConfig(dump_config, p_dm->dump_config))
                return false;
        }
        PARSE_JSON_FILED(flinger, control_device_thread, p_dm->control_device_thread, Bool);
        PARSE_JSON_FILED(flinger, use_perception_recenter, p_dm->use_perception_recenter, Bool);
        PARSE_JSON_FILED(flinger, gen_fake_vsync, p_dm->gen_fake_vsync, Bool);
        // Parse ATW detail collection configuration
        PARSE_JSON_FILED(flinger, enable_atw_detail_collection, p_dm->enable_atw_detail_collection, Bool);
        PARSE_JSON_FILED(flinger, max_atw_details_size, p_dm->max_atw_details_size, UInt);
        PARSE_JSON_FILED(flinger, atw_details_dump_dir, p_dm->atw_details_dump_dir, String);
        PARSE_JSON_FILED(flinger, dump_frame_head_transforms, p_dm->dump_frame_head_transforms, Bool);
        PARSE_JSON_FILED(flinger, dp_submit_line_count, p_dm->dp_submit_line_count, UInt);
        PARSE_JSON_FILED(flinger, default_stereo_dp_src, p_dm->default_stereo_dp_src, Bool);
        PARSE_JSON_FILED(flinger, dp_display_sync_line_count, p_dm->dp_display_sync_line_count, UInt);
        PARSE_JSON_FILED(flinger, gen_depth_shifted_frame, p_dm->gen_depth_shifted_frame, Bool);
        PARSE_JSON_FILED(flinger, async_shifted_frame, p_dm->async_shifted_frame, Bool);
        PARSE_JSON_FILED(flinger, mmz_cached_for_shifted_frame, p_dm->mmz_cached_for_shifted_frame, Bool);
        PARSE_JSON_FILED(flinger, register_signal_handler, p_dm->register_signal_handler, Bool);
        return true;
    }

    DebugManager::DebugManager()
        : dump_in_progress_(false)
    {
        debug_warp_matrices.resize(39 * 9 * sizeof(float));
        std::vector<float> identity_mat3f = {1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0};
        for (uint32_t i = 0; i < 39; i++)
        {
            std::memcpy(debug_warp_matrices.data() + i * 9 * sizeof(float), identity_mat3f.data(), 9 * sizeof(float));
        }
    }

    DebugManager::~DebugManager()
    {
        // Make sure any ongoing dump is completed before destruction
        std::lock_guard<std::mutex> lock(dump_mutex_);
        if (dump_thread_.joinable())
        {
            dump_thread_.join();
        }
    }

    static int s_fd_dptest_reg_base;
    static unsigned long s_va_base_reg_base;
    // map 0x08800000-08900000  len:0x100000
    void DebugManager::ar_dptest_system_register_map()
    {
#ifdef HERON_SYSTEM_XRLINUX
        s_fd_dptest_reg_base = open("/dev/mem", O_RDWR | O_SYNC);
        s_va_base_reg_base = (unsigned long)mmap(NULL, 0x100000, PROT_READ | PROT_WRITE, MAP_SHARED, s_fd_dptest_reg_base, 0x08800000);

        va_de_reg_base[0] = s_va_base_reg_base + 0x20000;
        va_de_reg_base[1] = s_va_base_reg_base + 0x40000;
        va_gdc_reg_base[0] = s_va_base_reg_base + 0x30000;
        va_gdc_reg_base[1] = s_va_base_reg_base + 0x50000;
#endif
    }

    void DebugManager::ar_dptest_system_register_unmap()
    {
#ifdef HERON_SYSTEM_XRLINUX
        munmap((unsigned char *)s_va_base_reg_base, 0x100000);
        close(s_fd_dptest_reg_base);
#endif
    }

    void DebugManager::CheckUnderflow(DisplayUsage display_usage)
    {
        if (!check_underflow)
            return;
        if (AR_DPTEST_GET_REG_BITS(DebugManager::GetInstance()->va_de_reg_base[display_usage] + 0x1518) & 0x20)
        {
            HERON_LOG_WARN("GDC{} underflow detected on vsync callback", display_usage);
        }
    }

    void DebugManager::DumpATWDetails()
    {
        if (atw_details.empty())
        {
            HERON_LOG_WARN("No ATW details to dump");
            return;
        }

        // Check if a dump is already in progress
        {
            std::lock_guard<std::mutex> lock(dump_mutex_);
            if (dump_in_progress_)
            {
                HERON_LOG_WARN("ATW details dump already in progress, skipping");
                return;
            }

            // Mark as in progress
            dump_in_progress_ = true;
        }

        // Generate a filename based on the current time
        char timestamp[64];
        time_t now = time(nullptr);
        struct tm *tm_info = localtime(&now);
        strftime(timestamp, sizeof(timestamp), "%Y%m%d_%H%M%S", tm_info);

        std::string filename = std::string("atw_details_") + timestamp + ".bin";
        std::string full_path = atw_details_dump_dir + "/" + filename;

        HERON_LOG_DEBUG("Starting async dump of {} ATW details to {}", atw_details.size(), full_path);

        // Make a copy of the data to be dumped
        std::vector<ATWFrameDetail> details_to_dump = atw_details;
        std::string dump_path = full_path;

        // Clear the vector immediately so we can continue collecting data
        atw_details.clear();

        // If there's an existing thread, wait for it to complete
        if (dump_thread_.joinable())
            dump_thread_.join();

        // Start a new thread to write the data
        dump_thread_ = std::thread(&DebugManager::DumpATWDetailsAsync, this, std::move(details_to_dump), dump_path);
    }

    void DebugManager::DumpATWDetailsAsync(std::vector<ATWFrameDetail> details_to_dump, std::string dump_path)
    {
        // This method runs in a separate thread

        // Ensure the directory exists
        size_t last_slash = dump_path.find_last_of('/');
        if (last_slash != std::string::npos)
        {
            std::string dir_path = dump_path.substr(0, last_slash);
            // Create directory if it doesn't exist (mkdir -p equivalent)
            std::string mkdir_cmd = "mkdir -p " + dir_path;
            int result = system(mkdir_cmd.c_str());
            if (result != 0)
            {
                HERON_LOG_WARN("Failed to create directory: {}, error code: {}", dir_path, result);
            }
        }

        std::ofstream file(dump_path, std::ios::binary);
        if (!file)
        {
            HERON_LOG_ERROR("Failed to open file for ATW details dump: {}", dump_path);

            // Mark as no longer in progress
            std::lock_guard<std::mutex> lock(dump_mutex_);
            dump_in_progress_ = false;
            return;
        }

        // Write the number of details
        uint32_t num_details = static_cast<uint32_t>(details_to_dump.size());
        HERON_LOG_DEBUG("first detail frame_num:{} start_render_ns:{} old_pose_ns:{} ", details_to_dump[0].frame_num, details_to_dump[0].start_render_ns, details_to_dump[0].old_pose_ns);
        PrintObject("first detail old_transform:", details_to_dump[0].old_transform);
        std::stringstream ss;
        for (int i = 0; i < 35; i++)
        {
            ss << details_to_dump[0].line_diff[i];
            if (i < 34)
                ss << ",";
        }
        HERON_LOG_DEBUG("first detail line_diff: {}", ss.str());
        file.write(reinterpret_cast<const char *>(&num_details), sizeof(num_details));

        // Write all details
        file.write(reinterpret_cast<const char *>(details_to_dump.data()), details_to_dump.size() * sizeof(ATWFrameDetail));

        file.close();

        HERON_LOG_DEBUG("ATW details dump completed, {} bytes written", details_to_dump.size() * sizeof(ATWFrameDetail));

        // Mark as no longer in progress
        std::lock_guard<std::mutex> lock(dump_mutex_);
        dump_in_progress_ = false;
    }

    void DebugManager::DumpFrameHeadTransforms(const std::vector<Transform> &frame_head_transforms_copy, uint32_t id)
    {
        if (!dump_frame_head_transforms)
            return;
        std::string filename = std::string(current_log_dir + "frame_head_transforms_") + std::to_string(id) + ".bin";
        std::ofstream file(filename, std::ios::binary | std::ios::app);
        if (!file)
        {
            HERON_LOG_ERROR("Failed to open file for frame_head_transforms dump");
            return;
        }
        file.write(reinterpret_cast<const char *>(frame_head_transforms_copy.data()), frame_head_transforms_copy.size() * sizeof(Transform));
        file.close();
    }
} // namespace heron
