#include <heron/util/image_tools.h>
#include <heron/util/log.h>

namespace heron
{
    bool ConvertImage(const Image &src, Image &dst, const Vector2f &scale_factor)
    {
        if (src.pixel_format != FRAMEBUFFER_FORMAT_BGRA_8888)
        {
            HERON_LOG_ERROR("Pixel format not supported");
            return false;
        }

        if (src.width != dst.width || src.height != dst.height)
        {
            HERON_LOG_ERROR("Image size not match");
            return false;
        }
        return true;
    }
}