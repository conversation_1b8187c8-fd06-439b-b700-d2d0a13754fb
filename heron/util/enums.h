#pragma once

enum FrameUsage
{
    FRAME_USAGE_ANY = 0,
    FRAME_USAGE_LEFT = 0,
    FRAME_USAGE_RIGHT = 1,
    FRAME_USAGE_COUNT = 2,
};
enum
{
    CALLBACK_BLOCK_CNT = 4,
    ROW_COUNT_IN_BLOCK = 32,
    COLUMN_COUNT_IN_BLOCK = 32,
    DP_FRAME_Y_STRIDE = 2048,
    DP_FRAME_UV_STRIDE = 1024,
    OSD_FRAME_STRIDE = 1024,
    DP_DUMMY_FRAME_WIDTH = 64,
    DP_DUMMY_FRAME_HEIGHT = 36,
    SRC_FRAME_WIDTH_DEFAULT = 1920,
    SRC_FRAME_HEIGHT_DEFAULT = 1080,
    SCREEN_WIDTH_DEFAULT = 1920,
    SCREEN_HEIGHT_DEFAULT = 1080,
};

/// AR94_SDK enums copytypedef enum arVO_IRQ_TYPE
enum XR_VO_IRQ_TYPE
{
    XR_IRQ_TYPE_VSYNC = 0,
    XR_IRQ_TYPE_INTERVAL_LINE = 1,
    XR_IRQ_TYPE_FRAME_DONE = 2,
};

/// AR94_SDK enums copytypedef enum arPIXEL_FORMAT_E
enum XR_PIXEL_FORMAT
{
    XR_PIXEL_FORMAT_YVU_PLANAR_420 = 23,
};

enum LateStageReprojectionMode
{
    //无需warp，就相当于透传，宝马适用于这种情况
    WARP_MODE_NONE = 0,
    //做畸变校正和空间warp
    //mono模式的跟头场景，需要畸变校正+空间warp（即将head warp到左右眼）
    //stereo的跟头场景则仅需做畸变校正
    WARP_MODE_SPACE = 1,
    //按照指定平面warp
    WARP_MODE_PLANE = 2,
    //按照深度图warp（ptw）
    WARP_MODE_DEPTH = 3
};

enum DpBufferMode {
    DP_BUFFER_MODE_UNKNOWN = 0,
    DP_BUFFER_MODE_SINGLE = 1,
    DP_BUFFER_MODE_MULTIPLE = 2,
};

enum DpSubmitType {
    DP_SUBMIT_TYPE_UNKNOWN = 0,
    DP_SUBMIT_TYPE_VSYNC = 1,
    DP_SUBMIT_TYPE_INTERVAL_LINE = 2,
    DP_SUBMIT_TYPE_FRAME_DONE = 3,
};

enum GlassesMode
{
    GLASSES_MODE_UNKNOWN = 0,
    GLASSES_MODE_DISPLAY = 1,
    GLASSES_MODE_LSR = 2,
    GLASSES_MODE_BYPASS = 3
};

enum HandType {
    HAND_TYPE_UNKNOWN = -1,
    HAND_TYPE_LEFT = 0,
    HAND_TYPE_RIGHT = 1,
    HAND_TYPE_COUNT = 2,
};

enum GestureType {
    GESTURE_TYPE_UNKNOWN = -1,
    GESTURE_TYPE_OPEN_HAND = 0,
    GESTURE_TYPE_GRAB = 1,
    GESTURE_TYPE_PINCH = 2,
    GESTURE_TYPE_POINT = 3,
    GESTURE_TYPE_VICTORY = 4,
    GESTURE_TYPE_CALL = 5,
    GESTURE_TYPE_SYSTEM = 6,
    GESTURE_TYPE_THUMBS_UP = 7,
    GESTURE_TYPE_INVERSE_PINCH = 7,
};

enum HandJointType {
    HAND_JOINT_TYPE_INVALID = -1,
    HAND_JOINT_TYPE_PALM = 0,
    HAND_JOINT_TYPE_WRIST = 1,
    HAND_JOINT_TYPE_THUMB_METACARPAL = 2,
    HAND_JOINT_TYPE_THUMB_PROXIMAL = 3,
    HAND_JOINT_TYPE_THUMB_DISTAL = 4,
    HAND_JOINT_TYPE_THUMB_TIP = 5,
    HAND_JOINT_TYPE_INDEX_FINGER_METACARPAL = 6,
    HAND_JOINT_TYPE_INDEX_FINGER_PROXIMAL = 7,
    HAND_JOINT_TYPE_INDEX_FINGER_INTERMEDIATE = 8,
    HAND_JOINT_TYPE_INDEX_FINGER_DISTAL = 9,
    HAND_JOINT_TYPE_INDEX_FINGER_TIP = 10,
    HAND_JOINT_TYPE_MIDDLE_FINGER_METACARPAL = 11,
    HAND_JOINT_TYPE_MIDDLE_FINGER_PROXIMAL = 12,
    HAND_JOINT_TYPE_MIDDLE_FINGER_INTERMEDIATE = 13,
    HAND_JOINT_TYPE_MIDDLE_FINGER_DISTAL = 14,
    HAND_JOINT_TYPE_MIDDLE_FINGER_TIP = 15,
    HAND_JOINT_TYPE_RING_FINGER_METACARPAL = 16,
    HAND_JOINT_TYPE_RING_FINGER_PROXIMAL = 17,
    HAND_JOINT_TYPE_RING_FINGER_INTERMEDIATE = 18,
    HAND_JOINT_TYPE_RING_FINGER_DISTAL = 19,
    HAND_JOINT_TYPE_RING_FINGER_TIP = 20,
    HAND_JOINT_TYPE_LITTLE_FINGER_METACARPAL = 21,
    HAND_JOINT_TYPE_LITTLE_FINGER_PROXIMAL = 22,
    HAND_JOINT_TYPE_LITTLE_FINGER_INTERMEDIATE = 23,
    HAND_JOINT_TYPE_LITTLE_FINGER_DISTAL = 24,
    HAND_JOINT_TYPE_LITTLE_FINGER_TIP = 25,
    HAND_JOINT_TYPE_MAX = 32,
};

