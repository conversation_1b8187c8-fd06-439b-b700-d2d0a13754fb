/**
 * @file nr_type_converter.h
 * @brief This file contains functions to convert between NR types and heron types
 *
 * Note that this file cannot be included together with heron/interface/device_api/nr_extra_xxx.h
 * <AUTHOR> sj<PERSON>@xreal.com
 * @date 07/04/2025
 */

#include <heron/util/types.h>
#include <heron/interface/include/public/nr_plugin_types.h>
#include <heron/interface/include/channel/nr_plugin_glasses_types.h>

namespace heron
{
    inline void ConvertToFov4f(Fov4f &out, const NRFov4f &in)
    {
        out.left_tan = in.left_tan;
        out.right_tan = in.right_tan;
        out.top_tan = in.top_tan;
        out.bottom_tan = in.bottom_tan;
    }

    inline void ConvertToVector3f(Vector3f &out, const NRVector3f &in)
    {
        out.x() = in.x;
        out.y() = in.y;
        out.z() = in.z;
    }

    inline void ConvertToNRVector3f(NRVector3f &out, const Vector3f &in)
    {
        out.x = in.x();
        out.y = in.y();
        out.z = in.z();
    }
    inline void ConvertToNRSize2i(NRSize2i &out, const Vector2i &in)
    {
        out.width = in.x();
        out.height = in.y();
    }

    inline void ConvertToVector2i(Vector2i &out, const NRSize2i &in)
    {
        out.x() = in.width;
        out.y() = in.height;
    }

    inline void ConvertToQuatf(Quatf &out, const NRQuatf &in)
    {
        out.x() = in.qx;
        out.y() = in.qy;
        out.z() = in.qz;
        out.w() = in.qw;
    }

    inline void ConvertToNRQuatf(NRQuatf &out, const Quatf &in)
    {
        out.qx = in.x();
        out.qy = in.y();
        out.qz = in.z();
        out.qw = in.w();
    }

    inline void ConvertToNRRectf(NRRectf &out, const Rectf &in)
    {
        out.bottom = in.bottom;
        out.top = in.top;
        out.left = in.left;
        out.right = in.right;
    }
    inline void ConvertToRectf(Rectf &out, const NRRectf &in)
    {
        out.bottom = in.bottom;
        out.top = in.top;
        out.left = in.left;
        out.right = in.right;
    }

    inline void NRTransformToTransform(Transform &out, const NRTransform &in)
    {
        ConvertToVector3f(out.position, in.position);
        ConvertToQuatf(out.rotation, in.rotation);
    }

    inline void TransformToNRTransform(NRTransform &out, const Transform &in)
    {
        ConvertToNRVector3f(out.position, in.position);
        ConvertToNRQuatf(out.rotation, in.rotation);
    }

    inline void ConvertToNRFrameBufferQueue(NRFrameBufferQueue &out, FramebufferQueue &in)
    {
        out.buffer_count = in.buffer_count;
        out.buffer_queue = in.buffer_queue;
    }

    inline void ConvertToResolutionInfo(ResolutionInfo &out, const NRResolutionInfo &in)
    {
        out.width = in.width;
        out.height = in.height;
        out.refresh_rate = in.refresh_rate;
    }

    inline void EdidToSizePixel(Vector2i &out, const Edid &in)
    {
        switch (in)
        {
        case EDID_1920_1080_60:
        case EDID_1920_1080_72:
        case EDID_1920_1080_90:
        case EDID_1920_1080_120:
        case EDID_1920_1080_60_DEFAULT:
            out.x() = 1920;
            out.y() = 1080;
            break;
        case EDID_3840_1080_60:
        case EDID_3840_1080_72:
        case EDID_3840_1080_90:
        case EDID_3840_1080_120:
            out.x() = 3840;
            out.y() = 1080;
            break;
        case EDID_1920_1200_60:
        case EDID_1920_1200_72:
        case EDID_1920_1200_90:
        case EDID_1920_1200_120:
        case EDID_1920_1200_90_DEFAULT:
            out.x() = 1920;
            out.y() = 1200;
            break;
        case EDID_3840_1200_60:
        case EDID_3840_1200_72:
        case EDID_3840_1200_90:
        case EDID_3840_1200_120:
            out.x() = 3840;
            out.y() = 1200;
            break;
        case EDID_2560_1080_60:
        case EDID_2560_1080_72:
        case EDID_2560_1080_75:
        case EDID_2560_1080_90:
        case EDID_2560_1080_120:
            out.x() = 2560;
            out.y() = 1080;
            break;
        case EDID_2560_1200_60:
        case EDID_2560_1200_72:
        case EDID_2560_1200_75:
        case EDID_2560_1200_90:
        case EDID_2560_1200_120:
            out.x() = 2560;
            out.y() = 1200;
            break;
        default:
            out.x() = SRC_FRAME_WIDTH_DEFAULT;
            out.y() = SRC_FRAME_HEIGHT_DEFAULT;
            break;
        }
    }

    inline void ConvertToNRDpDisplayConfig(NRDpDisplayConfig &out, const DpDisplayConfig &in)
    {
        out.dp_display_sync_line_count = in.dp_display_sync_line_count;
        out.lines64_enable = in.lines64_enable;
        out.display_resolution = (NRResolution)in.display_resolution;
    }

    inline void ConverToDpDisplayConfig(DpDisplayConfig &out, const NRDpDisplayConfig &in)
    {
        out.dp_display_sync_line_count = in.dp_display_sync_line_count;
        out.lines64_enable = in.lines64_enable;
        out.display_resolution = (Resolution)in.display_resolution;
    }

    inline void ConvertToNRDpConfig(NRDpConfig &out, const DpConfig &in)
    {
        out.buffer_mode = (NRDpBufferMode)in.buffer_mode;
        out.submit_type = (NRDpSubmitType)in.submit_type;
        out.submit_line_count = in.submit_line_count;
    }

    inline void ConvertToDpConfig(DpConfig &out, const NRDpConfig &in)
    {
        out.buffer_mode = (DpBufferMode)in.buffer_mode;
        out.submit_type = (DpSubmitType)in.submit_type;
        out.submit_line_count = in.submit_line_count;
    }

    inline void ConvertToDpFrameData(DpFrameData &out, const NRDpFrameData &in)
    {
        out.frame_id = in.frame_id;
        out.width = in.width;
        out.height = in.height;
        out.pixel_format = (FramebufferFormat)in.pixel_format;
        out.data[0] = (char *)in.data.x;
        out.data[1] = (char *)in.data.y;
        out.data[2] = (char *)in.data.z;
        out.data_ext[0] = (char *)in.data_ext.x;
        out.data_ext[1] = (char *)in.data_ext.y;
        out.data_ext[2] = (char *)in.data_ext.z;
        out.header[0] = (char *)in.header.x;
        out.header[1] = (char *)in.header.y;
        out.header[2] = (char *)in.header.z;
        out.header_ext[0] = (char *)in.header_ext.x;
        out.header_ext[1] = (char *)in.header_ext.y;
        out.header_ext[2] = (char *)in.header_ext.z;
        out.strides[0] = in.strides.x;
        out.strides[1] = in.strides.y;
        out.strides[2] = in.strides.z;
        out.pts = in.pts;
        out.ar_frame_handle = in.ar_frame_handle;
    }

    inline void ConvertToNRDpFrameData(NRDpFrameData &out, const DpFrameData &in)
    {
        out.frame_id = in.frame_id;
        out.width = in.width;
        out.height = in.height;
        out.pixel_format = (NRFrameBufferFormat)in.pixel_format;
        out.data.x = (uint64_t)in.data[0];
        out.data.y = (uint64_t)in.data[1];
        out.data.z = (uint64_t)in.data[2];
        out.data_ext.x = (uint64_t)in.data_ext[0];
        out.data_ext.y = (uint64_t)in.data_ext[1];
        out.data_ext.z = (uint64_t)in.data_ext[2];
        out.header.x = (uint64_t)in.header[0];
        out.header.y = (uint64_t)in.header[1];
        out.header.z = (uint64_t)in.header[2];
        out.header_ext.x = (uint64_t)in.header_ext[0];
        out.header_ext.y = (uint64_t)in.header_ext[1];
        out.header_ext.z = (uint64_t)in.header_ext[2];
        out.strides.x = (uint64_t)in.strides[0];
        out.strides.y = (uint64_t)in.strides[1];
        out.strides.z = (uint64_t)in.strides[2];
        out.pts = in.pts;
        out.ar_frame_handle = in.ar_frame_handle;
    }

    inline void ConvertToHandJointData(HandJointData &out, const NRHandJointData &in)
    {
        out.version = in.version;
        out.hand_joint_type = (HandJointType)in.hand_joint_type;
        NRTransformToTransform(out.hand_joint_pose, in.hand_joint_pose);
    }

    inline void ConvertToHandData(HandData &out, const NRHandData &in) {
        out.version = in.version;
        out.is_tracked = in.is_tracked;
        out.confidence = in.confidence;
        out.hand_type = (HandType)in.hand_type;
        out.gesture_type = (GestureType)in.gesture_type;
        out.hand_joint_count = in.hand_joint_count;
        for (uint i = 0; i < in.hand_joint_count; i++) {
            ConvertToHandJointData(out.hand_joint_data[i], in.hand_joint_data[i]);
        }
        out.image_timestamp_nanos = in.image_timestamp_nanos;
        out.pinch_strength = in.pinch_strength;
    }

}