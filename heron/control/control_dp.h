#pragma once

#include <heron/util/types.h>
#include <heron/model/model.h>

#include <framework/util/singleton.h>

#include <atomic>

namespace heron::control
{
    class DpCtrl : public framework::util::Singleton<DpCtrl>
    {
    public:
        void Start();
        void Stop();
        void StartPresentDpFrames(const ResolutionInfo &res_info);
        void StopPresentDpFrames();

        bool ParseFrame(model::FrameInfo &frame_info);
        bool ParseEmbeddedData(model::FrameInfo &frame_info, bool &use_simple,
                               FrameEmbeddedInfoSimpleTwin &embedded_info_simple, FrameEmbeddedInfoTwin &embedded_info);
        bool PopulateFrameInfoMetadata(model::FrameInfo &frame_info, bool use_simple,
                                       const FrameEmbeddedInfoSimpleTwin &embedded_info_simple,
                                       const FrameEmbeddedInfoTwin &embedded_info);
        void UpdateSceneMode(SceneMode scene_mode);
        void UpdateSuitableSrcFrameSize(uint32_t curr_width, uint32_t curr_height,
                                        const model::SpaceScreenStatus &space_screen_status, const Vector2f &target_size_factor);

        void SetGlassesMode(GlassesMode mode);

        bool AllocShiftedFrame();
        bool AcquireWritableDpFrameData(const DpFrameData **dst_frame_data);
        bool SubmitStereoDpFrameData(const DpFrameData *left_frame_data, const DpFrameData *right_frame_data);

    private:
        SceneMode curr_frame_scene_mode_ = SCENE_MODE_SPACE_SCREEN;

    private:
        void PrintPopulatedMetadata(const model::FrameInfo &frame_info, bool embedded_simple);

    public:
        void ModifyFrameContent(model::FrameInfo &frame_info);
        void MaybeDumpFrame(const model::FrameInfo &frame_info, bool bw_decode_result);

    private:
        std::atomic<bool> dp_render_stop_{true};
        uint32_t parse_frame_count_ = 0;
    };
} // namespace heron::control
