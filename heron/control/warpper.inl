#include <heron/interface_provider/flinger.h>
#include <heron/dispatch/gdc_manager.h>
#include <heron/control/warpper.h>
#include <heron/control/control_display.h>
#include <heron/model/model_manager.h>
#include <heron/util/warp.h>
#include <heron/util/debug.h>
#include <heron/util/counter.h>
#include <heron/control/control_dp.h>

#include <heron/env/export.h>

#include <framework/util/trace_manager.h>

using namespace heron;
using namespace model;
using namespace dispatch;
using namespace control;
using namespace warp;
using namespace interface_provider;

HERON_FORCE_INLINE void Warpper::PrintTimingVerbose(const FrameMetadataInternal &metadata)
{
    HERON_LOG_DEBUG("frame_num: {} start_render: {} start_comp: {} recv_dp: {} pred_pose: {} disp_mid: {} apply: {}",
                    metadata.frame_number,
                    metadata.timing.start_render_ns,
                    metadata.timing.data_device_ns,
                    metadata.timing.dp_rx_done_ns,
                    metadata.timing.device_pose_ns,
                    metadata.timing.mid_display_ns,
                    metadata.timing.frame_apply_ns);
    // HERON_LOG_DEBUG("frame_number: {} data_recv: {} vsync_gen: {} vsync_sent: {} embedded_parsed: {} frame_parsed: {}",
    //                 metadata.frame_number,
    //                 metadata.timing.data_recv_ns,
    //                 metadata.timing.vsync_generated_ns,
    //                 metadata.timing.vsync_sent_ns,
    //                 metadata.timing.embedded_data_parsed_ns,
    //                 GetTimeNano());
}

static Average s_predict_to_present("predict_to_present");
static Average s_chip_est_ns("chip_est_ns");
HERON_FORCE_INLINE void Warpper::PrepareWarpForBlock(uint32_t interrupt_id, SpaceScreenStatusPtr status, DisplayUsage display_usage,
                                                     uint32_t target_block_id, uint64_t timestamp_nanos)
{
    F_TRACE_EVENT("display_ctrl", "prepare_warp_for_block");
    DebugManager *p_dm = DebugManager::GetInstance();
    ModelManager *p_mm = ModelManager::GetInstance();
    if (status->validation.InvalidFrame())
        return;
    uint64_t target_buffer_phy;
    void *target_buffer_vir;
    if (p_dm->mesh_only_warp)
        GDCManager::GetInstance()->GetMeshBuffer(static_cast<GDC_USAGE>(display_usage), &target_buffer_phy, &target_buffer_vir);
    else
        GDCManager::GetInstance()->GetMatrixBuffer(static_cast<GDC_USAGE>(display_usage), &target_buffer_phy, &target_buffer_vir);
    Transform to_transform, head_transform, eye_from_head; // Transform objects are initialized as identity defaultly
    target_block_id %= p_mm->display_metadatas_[DISPLAY_USAGE_LEFT].distortion_info.num_rows;
    uint64_t now = 0;
    if (p_dm->enable_atw_detail_collection)
        now = GetTimeNano();
    DisplayCtrl::GetInstance()->UpdateHeadTransform(head_transform, interrupt_id, display_usage, target_block_id, timestamp_nanos);
    if (status->validation.NotToPresent())
        return;
    // 透传模式，无需计算以下信息
    if (status->lsr_mode == WARP_MODE_NONE)
        return;
    eye_from_head = (status->scene_mode == SCENE_MODE_WITH_NEBULA) ? p_mm->host_pose_from_head_[display_usage] : p_mm->display_pose_from_head_[display_usage];
    TransformToEyePose(head_transform, eye_from_head, to_transform);
    if (p_dm->enable_atw_detail_collection)
        PopulateATWFrameDetail(status, display_usage, target_block_id, now, timestamp_nanos, to_transform);
    PrepareWarpMeshPoint(status, to_transform, display_usage, target_block_id, (float *)target_buffer_phy, (float *)target_buffer_vir);
    if (!p_dm->use_async_warp && !p_dm->only_use_left_vo_callback)
        return;
    if (p_dm->mesh_only_warp)
        GDCManager::GetInstance()->GetMeshBuffer(GDC_USAGE_RIGHT_DISPLAY, &target_buffer_phy, &target_buffer_vir);
    else
        GDCManager::GetInstance()->GetMatrixBuffer(GDC_USAGE_RIGHT_DISPLAY, &target_buffer_phy, &target_buffer_vir);
    eye_from_head = (status->scene_mode == SCENE_MODE_WITH_NEBULA) ? p_mm->host_pose_from_head_[DISPLAY_USAGE_RIGHT] : p_mm->display_pose_from_head_[DISPLAY_USAGE_RIGHT];
    TransformToEyePose(head_transform, eye_from_head, to_transform);
    PrepareWarpMeshPoint(status, to_transform, DISPLAY_USAGE_RIGHT, target_block_id, (float *)target_buffer_phy, (float *)target_buffer_vir);

    if (p_dm->debug_log.target_block_id)
    {
        if (frame_count_ % p_dm->print_frame_interval == 0)
        {
            HERON_LOG_DEBUG("interrupt_id: {} target_block_id:{}", interrupt_id, target_block_id);
        }
    }
    if (p_dm->debug_log.timing_verbose)
    {
        if (status->scene_mode == SCENE_MODE_WITH_NEBULA && display_usage == DISPLAY_USAGE_LEFT && target_block_id == 17)
        {
            DisplayCtrl::GetInstance()->GetLatestFrameInfo()->metadata.timing.mid_display_ns = timestamp_nanos;
            PrintTimingVerbose(DisplayCtrl::GetInstance()->GetLatestFrameInfo()->metadata);
        }
    }
    if (p_dm->profile_atw)
    {
        if (status->scene_mode == SCENE_MODE_WITH_NEBULA && display_usage == DISPLAY_USAGE_LEFT)
        {
            s_predict_to_present.Update(timestamp_nanos - DisplayCtrl::GetInstance()->GetLatestFrameInfo()->metadata.timing.device_pose_ns);
            s_chip_est_ns.Update(timestamp_nanos - GetTimeNano());
        }
    }
}

HERON_FORCE_INLINE void Warpper::PrepareWarpForInterrupt(uint32_t job_id, uint32_t begin_block_id, uint32_t end_block_id,
                                                         uint64_t base_timestamp_ns, SpaceScreenStatusPtr status, DisplayUsage display_usage)
{
    uint64_t timestamp_nanos = 0;
    // uint64_t warp_start_ns = GetTimeNano();
    for (uint32_t target_block_id = begin_block_id; target_block_id < end_block_id; target_block_id++)
    {
        timestamp_nanos = base_timestamp_ns + target_block_id * BLOCK_REFRESH_TIME_NS;
        PrepareWarpForBlock(job_id, status, display_usage, target_block_id, timestamp_nanos);
    }
    // warp_time_cost_[display_usage].Update(GetTimeNano() - warp_start_ns);
    if (!DebugManager::GetInstance()->debug_log.warp_job_block_id)
        return;
    if (display_usage == DISPLAY_USAGE_LEFT && frame_count_ % DebugManager::GetInstance()->print_frame_interval == 0)
    {
        HERON_LOG_DEBUG("warp_job:{} target_block_id:[{},{}] base_timestamp:{}", job_id, begin_block_id, end_block_id - 1, base_timestamp_ns);
    }
}

HERON_FORCE_INLINE void Warpper::PrepareWarpInternal(uint64_t curr_vsync_time_ns, SpaceScreenStatusPtr status, DisplayUsage display_usage,
                                                     uint32_t interrupt_id, const XR_VO_CALLBACK_INFO &callback_info)
{
    if (display_usage == DISPLAY_USAGE_LEFT && callback_info.callback_type == XR_IRQ_TYPE_VSYNC) // only count/print at vsync callback
    {
        frame_count_++;
        if (frame_count_ % DebugManager::GetInstance()->print_frame_interval == 0)
        {
            HERON_LOG_DEBUG("[warp_timing] offset:{}({}) special:{}({}) get_frame_and_gdc_conf:{}({}) blk_calc:{} overlap:{:.2f} overwrite:{} total:{}",
                            BLOCK_ID_OFFSET, BLOCK_ID_OFFSET * ROW_COUNT_IN_BLOCK,
                            SPECIAL_INTERRUPT_ID, (SPECIAL_INTERRUPT_ID + 1) * ROW_COUNT_IN_BLOCK * CALLBACK_BLOCK_CNT,
                            GET_FRAME_AND_CONFIG_GDC_AT, (GET_FRAME_AND_CONFIG_GDC_AT + 1) * ROW_COUNT_IN_BLOCK * CALLBACK_BLOCK_CNT,
                            WARP_BLOCK_CNT_AT_EACH_CALLBACK, WARP_BLOCK_CNT_AT_EACH_CALLBACK / (float)CALLBACK_BLOCK_CNT,
                            OVERWRITE_BLOCK_CNT, TOTAL_CALLBACKS);
        }
    }
    uint64_t base_timestamp_nanos = curr_vsync_time_ns + V_BLANK_NS + DISPLAY_DUTY / 2;
    uint32_t begin_block_id, end_block_id;
    if (callback_info.callback_type == XR_IRQ_TYPE_VSYNC)
    {
        begin_block_id = BLOCK_ID_OFFSET + BLOCK_LEFT_ON_SPECIAL_INTERRUPT - OVERWRITE_BLOCK_CNT;
        end_block_id = begin_block_id + WARP_BLOCK_CNT_AT_EACH_CALLBACK - BLOCK_LEFT_ON_SPECIAL_INTERRUPT + 1;
        PrepareWarpForInterrupt(0, begin_block_id, end_block_id, base_timestamp_nanos, status, display_usage);
        return;
    }
    if ((int32_t)interrupt_id < SPECIAL_INTERRUPT_ID)
    {
        begin_block_id = 1 + (interrupt_id + 1) * CALLBACK_BLOCK_CNT + BLOCK_ID_OFFSET - OVERWRITE_BLOCK_CNT;
        end_block_id = begin_block_id + WARP_BLOCK_CNT_AT_EACH_CALLBACK;
        PrepareWarpForInterrupt(interrupt_id + 1, begin_block_id, end_block_id, base_timestamp_nanos, status, display_usage);
        return;
    }
    if ((int32_t)interrupt_id == SPECIAL_INTERRUPT_ID)
    {
        begin_block_id = 1 + (interrupt_id + 1) * CALLBACK_BLOCK_CNT + BLOCK_ID_OFFSET - OVERWRITE_BLOCK_CNT;
        end_block_id = 1 + (interrupt_id + 1) * CALLBACK_BLOCK_CNT + BLOCK_ID_OFFSET + BLOCK_LEFT_ON_SPECIAL_INTERRUPT;
        PrepareWarpForInterrupt(interrupt_id + 1, begin_block_id, end_block_id, base_timestamp_nanos, status, display_usage);
        /************* for next frame *****************/
        if (display_usage == DISPLAY_USAGE_LEFT)
            UpdateTiming(status);
        DisplayCtrl::GetInstance()->ApplyNextFrameStatus(display_usage, GET_FRAME_AND_CONFIG_GDC_AT);
        if (DebugManager::GetInstance()->use_async_warp || DebugManager::GetInstance()->only_use_left_vo_callback)
            DisplayCtrl::GetInstance()->ApplyNextFrameStatus(DISPLAY_USAGE_RIGHT, GET_FRAME_AND_CONFIG_GDC_AT);
        // object value status is pointing to may change after ApplyNextFrameStatus
        begin_block_id = 0;
        end_block_id = WARP_BLOCK_CNT_AT_EACH_CALLBACK - (BLOCK_LEFT_ON_SPECIAL_INTERRUPT + OVERWRITE_BLOCK_CNT) + 1; // +1 at for beginning of next frame
        base_timestamp_nanos = curr_vsync_time_ns + V_BLANK_NS + FRAME_INTERVAL_NS + DISPLAY_DUTY / 2;
        PrepareWarpForInterrupt(interrupt_id + 2, begin_block_id, end_block_id, base_timestamp_nanos, status, display_usage);
        return;
    }
    // interrupt_id > SPECIAL_INTERRUPT_ID
    begin_block_id = (interrupt_id - SPECIAL_INTERRUPT_ID) * CALLBACK_BLOCK_CNT - OVERWRITE_BLOCK_CNT - BLOCK_LEFT_ON_SPECIAL_INTERRUPT;
    end_block_id = begin_block_id + WARP_BLOCK_CNT_AT_EACH_CALLBACK;
    if ((int32_t)interrupt_id == SPECIAL_INTERRUPT_ID + 1)
        ++begin_block_id; // +1 to skip the block on special interrupt
    base_timestamp_nanos = curr_vsync_time_ns + V_BLANK_NS + FRAME_INTERVAL_NS + DISPLAY_DUTY / 2;
    PrepareWarpForInterrupt(interrupt_id + 2, begin_block_id, end_block_id, base_timestamp_nanos, status, display_usage);
}

HERON_FORCE_INLINE void Warpper::UpdateTiming(SpaceScreenStatusPtr status)
{
    ModelManager *p_mm = ModelManager::GetInstance();
    DebugManager *p_dm = DebugManager::GetInstance();
    FRAME_INTERVAL_NS = p_mm->GetDisplayFrameRefreshInterval();
    BLOCK_REFRESH_TIME_NS = p_mm->GetDisplayBlockRefreshInterval();
    V_BLANK_NS = p_mm->GetDisplayVBlank();
    DISPLAY_DUTY = FRAME_INTERVAL_NS * p_mm->GetDisplayDutyValue() / 100;
    WarpTiming timing = p_dm->normal_timing;
    if (status->space_mode == SPACE_MODE_ULTRA_WIDE && status->scene_mode == SCENE_MODE_SPACE_SCREEN)
        timing = p_dm->ultra_wide_timing;
    if (status->scene_mode == SCENE_MODE_WITH_NEBULA)
        timing = p_dm->with_nebula_timing;
    bool camera_occupied = false;
    FlingerInterface::GetInstance()->RgbCameraIsOccupied(&camera_occupied);
    if (camera_occupied)
        timing = p_dm->camera_occupied_timing;
    OVERWRITE_BLOCK_CNT = timing.overwrite_block_cnt;
    WARP_BLOCK_CNT_AT_EACH_CALLBACK = timing.warp_block_cnt_at_each_callback;
    BLOCK_ID_OFFSET = timing.warp_delay_block_cnt;
    // at SPECIAL_INTERRUPT_ID blocks left to be prepared for the current frame is less than CALLBACK_BLOCK_CNT
    SPECIAL_INTERRUPT_ID = (TOTAL_BLOCK_CNT - BLOCK_ID_OFFSET) / CALLBACK_BLOCK_CNT - 1;
    BLOCK_LEFT_ON_SPECIAL_INTERRUPT = TOTAL_BLOCK_CNT + 1 - (1 + (SPECIAL_INTERRUPT_ID + 1) * CALLBACK_BLOCK_CNT + BLOCK_ID_OFFSET);
    GET_FRAME_AND_CONFIG_GDC_AT = SPECIAL_INTERRUPT_ID - timing.get_frame_before_special;
}

HERON_FORCE_INLINE void Warpper::PrepareWarp(uint64_t curr_vsync_time_ns, SpaceScreenStatusPtr status, DisplayUsage display_usage,
                                             uint32_t interrupt_id, const XR_VO_CALLBACK_INFO &callback_info)
{
    F_TRACE_EVENT("display_ctrl", "prepare_warp");
    if (DebugManager::GetInstance()->only_use_left_vo_callback || DebugManager::GetInstance()->use_async_warp)
    {
        if (display_usage == DISPLAY_USAGE_RIGHT)
            return;
    }
    if (DebugManager::GetInstance()->use_async_warp)
        task_queue_.AddTask([this, curr_vsync_time_ns, status, display_usage, interrupt_id, callback_info]()
                            { this->PrepareWarpInternal(curr_vsync_time_ns, status, display_usage, interrupt_id, callback_info); });
    else
        PrepareWarpInternal(curr_vsync_time_ns, status, display_usage, interrupt_id, callback_info);
}

HERON_FORCE_INLINE void Warpper::PopulateATWFrameDetail(SpaceScreenStatusPtr status, DisplayUsage display_usage, uint32_t target_block_id,
                                                        uint64_t now, uint64_t timestamp_nanos, const Transform &to_transform)
{
    if (status->scene_mode != SCENE_MODE_WITH_NEBULA || display_usage != DISPLAY_USAGE_LEFT)
        return;
    FrameMetadataInternal metadata = DisplayCtrl::GetInstance()->GetLatestFrameInfo()->metadata;
    if (metadata.metadata_vec.size() != 2)
    {
        HERON_LOG_WARN("metadata_vec size:{} not as expected. frame_number:{}", metadata.metadata_vec.size(), metadata.frame_number);
        return;
    }
    DebugManager *p_dm = DebugManager::GetInstance();
    if (target_block_id == 0)
    {
        if (p_dm->atw_details.size() >= p_dm->max_atw_details_size)
            p_dm->DumpATWDetails();
        // Create and populate ATWFrameDetail object
        DebugManager::ATWFrameDetail detail;
        detail.frame_num = static_cast<uint32_t>(metadata.frame_number);
        detail.old_transform = metadata.metadata_vec[display_usage].pose;
        detail.start_render_ns = metadata.timing.start_render_ns;
        detail.old_pose_ns = metadata.timing.device_pose_ns;
        // Add to vector
        p_dm->atw_details.emplace_back(detail);
    }
    // Only collect data for the current block
    p_dm->atw_details.back().device_warp_ns[target_block_id] = now;
    p_dm->atw_details.back().device_predict_ns[target_block_id] = timestamp_nanos;
    // Initialize line_diff to 0
    p_dm->atw_details.back().line_diff[target_block_id] = 0;
    // Store the new transform
    p_dm->atw_details.back().new_transform[target_block_id] = to_transform;
}
