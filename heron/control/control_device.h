#pragma once

#include <heron/util/types.h>
#include <heron/util/counter.h>

#include <framework/util/singleton.h>

#include <vector>
#include <atomic>
#include <thread>

namespace heron::control
{
    class DeviceCtrl : public framework::util::Singleton<DeviceCtrl>
    {
    public:
        void Start();
        void Stop();
    private:
        void SetEcTransparent();
        void ResumeEcLevel();
        void CheckIsLookingAt();
        void CheckCanvasCornerPosition();

    private:
        int32_t cached_ec_level_ = 0;
        int32_t TRANSPARENT_COUNT = 1;
        int32_t RESUME_COUNT = 1;
        int32_t looking_at_count_ = 0;
        int32_t not_looking_at_count_ = 0;
        bool transparent_ = false;
        std::thread thread_;
        std::atomic<bool> running_{false};
        void Monitor();
        void ReceiveHandData();

        Average hand_data_latency_{"hand_data_latency"};
        CallCounter hand_data_fps_{"hand_data", 100, 0.0f, std::numeric_limits<float>::max()};
    };

} // namespace heron::control

