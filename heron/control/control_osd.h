#pragma once

#include <heron/util/types.h>

#include <framework/util/singleton.h>

namespace heron::control
{
    class OSDCtrl : public framework::util::Singleton<OSDCtrl>
    {
    public:
        void Start();
        void Stop();

        bool SubmitOSDFramebuffer(const void *left_buffer_payload, const void *right_buffer_payload);
        void ClearCurrentBuffer();
    private:

    };

} // namespace heron::control
