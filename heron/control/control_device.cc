#include <heron/control/control_device.h>
#include <heron/control/control_display.h>
#include <heron/interface_provider/device.h>
#include <heron/interface_provider/flinger.h>
#include <heron/util/debug.h>
#include <heron/util/log.h>

using namespace heron;
using namespace heron::control;
using namespace heron::interface_provider;

void DeviceCtrl::Start()
{
    // for debug only
    if (!DebugManager::GetInstance()->control_device_thread)
        return;
    if (running_)
        return;
    running_ = true;
    thread_ = std::thread(&DeviceCtrl::ReceiveHandData, this);
}

void DeviceCtrl::Stop()
{
    HERON_LOG_INFO("DeviceCtrl Stopping...")
    if (!running_)
        return;
    running_ = false;
    if (thread_.joinable())
    {
        thread_.join();
    }
    HERON_LOG_INFO("DeviceCtrl Stopped...")
}

void DeviceCtrl::Monitor()
{
    HERON_LOG_DEBUG("DeviceCtrl Monitor thread start");
    while (running_)
    {
        // CheckIsLookingAt();
        CheckCanvasCornerPosition();

        usleep(500 * 1000);
    }
    HERON_LOG_DEBUG("DeviceCtrl Monitor thread quit");
}

void DeviceCtrl::ReceiveHandData()
{
    HERON_LOG_DEBUG("DeviceCtrl ReceiveHandData thread start");
    while (running_)
    {
        uint64_t now = GetTimeNano();
        HandData hand_datas[2];
        uint32_t hand_num = 0;
        FlingerInterface::GetInstance()->GetHandData(now, hand_datas, &hand_num);
        bool is_first_tracked = true;
        hand_data_fps_.Update();
        for (uint32_t i = 0; i < hand_num; i++)
        {
            if (hand_datas[i].hand_type != HAND_TYPE_UNKNOWN && hand_datas[i].is_tracked)
            {
                if (is_first_tracked)
                {
                    is_first_tracked = false;
                    hand_data_latency_.Update(now - hand_datas[i].image_timestamp_nanos);
                }
                if (DebugManager::GetInstance()->debug_log.hand_data_detail)
                {
                    std::string hand_tag = "hand_data" + std::to_string(hand_datas[i].hand_type);
                    PerceptionType perception_type = ModelManager::GetInstance()->GetLocalPerceptionType();
                    Transform head_transform;
                    if (FlingerInterface::GetInstance()->GetDevicePose(perception_type, now, TRACKING_POSE_TYPE_LOW_LATENCY, &head_transform))
                    {
                        Vector3f hand_position_in_world = head_transform.rotation * hand_datas[i].hand_joint_data[0].hand_joint_pose.position + head_transform.position;
                        PrintObject(hand_tag + " in world", hand_position_in_world);
                    }
                    PrintObject(hand_tag, hand_datas[i].hand_joint_data[0].hand_joint_pose.position);
                }
            }
        }
        usleep(20 * 1000);
    }
    HERON_LOG_DEBUG("DeviceCtrl Monitor thread quit");
}

void DeviceCtrl::CheckIsLookingAt()
{
    bool is_looking_at_inner, is_looking_at_outer;
    DisplayCtrl::GetInstance()->IsLookingAt(is_looking_at_inner, is_looking_at_outer);
    // HERON_LOG_TRACE("intersection is_looking_at_inner:{} outer:{}", is_looking_at_inner, is_looking_at_outer);
    bool is_looking_at = transparent_ ? is_looking_at_inner : is_looking_at_outer;
    if (is_looking_at)
    {
        looking_at_count_++;
        not_looking_at_count_ = 0;
    }
    else
    {
        looking_at_count_ = 0;
        not_looking_at_count_++;
    }
    if (transparent_ && looking_at_count_ >= RESUME_COUNT)
    {
        ResumeEcLevel();
        transparent_ = false;
    }
    if (!transparent_ && not_looking_at_count_ >= TRANSPARENT_COUNT)
    {
        SetEcTransparent();
        transparent_ = true;
    }
}

void DeviceCtrl::CheckCanvasCornerPosition()
{
    // DisplayCtrl::GetInstance()->CheckCanvasCorners();
}
void DeviceCtrl::SetEcTransparent()
{
    DeviceInterface::GetInstance()->GetEcLevel(&cached_ec_level_);
    if (cached_ec_level_ == 1)
        DeviceInterface::GetInstance()->UpdateEcLevel(OPERATION_DECREASE);
    if (cached_ec_level_ == 2)
        DeviceInterface::GetInstance()->UpdateEcLevel(OPERATION_INCREASE);
}
void DeviceCtrl::ResumeEcLevel()
{
    if (cached_ec_level_ == 1)
        DeviceInterface::GetInstance()->UpdateEcLevel(OPERATION_INCREASE);
    if (cached_ec_level_ == 2)
        DeviceInterface::GetInstance()->UpdateEcLevel(OPERATION_DECREASE);
}
