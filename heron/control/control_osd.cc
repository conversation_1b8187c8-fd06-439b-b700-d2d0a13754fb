#include <heron/control/control_osd.h>
#include <heron/model/model_manager.h>
#include <heron/dispatch/dpu_manager.h>
#include <heron/dispatch/gdc_manager.h>
#include <heron/dispatch/dispatcher_wrapper.h>
#include <heron/util/warp.h>
#include <heron/util/debug.h>
#include <heron/util/log.h>

#include <framework/util/util.h>

using namespace heron::control;
using namespace heron::model;
using namespace heron::dispatch;

void OSDCtrl::Start()
{
    // HERON_LOG_TRACE("OSDCtrl Starting...");
    // HERON_LOG_TRACE("OSDCtrl Started.");
}

void OSDCtrl::Stop()
{
    // HERON_LOG_TRACE("OSDCtrl Stopping...");
    // HERON_LOG_TRACE("OSDCtrl Stopped.");
}

bool OSDCtrl::SubmitOSDFramebuffer(const void *left_buffer_payload, const void *right_buffer_payload)
{
    DebugManager *p_dm = DebugManager::GetInstance();
    if (p_dm->debug_log.osd_submit)
    {
        HERON_LOG_DEBUG("calling {}", __FUNCTION__);
    }
    ModelManager::GetInstance()->SubmitOSDFrame(DISPLAY_USAGE_LEFT, left_buffer_payload);
    ModelManager::GetInstance()->SubmitOSDFrame(DISPLAY_USAGE_RIGHT, right_buffer_payload);
    /**
     * if osd_frame_bypass_gdc, there will be no thread to do warpping for submitted osd frame.
     * No thread sending warpped frame to overlay either.
     * So, we have to push submitted frame directely.
     */
    OverlayFrameDataPtr osd_frame_info = ModelManager::GetInstance()->GetOSDReadableBuffer(DISPLAY_USAGE_LEFT);
    if (!DispatcherWrapper::GetInstance()->SendOverlayFrame(DISPLAY_USAGE_LEFT, osd_frame_info.get()))
    {
        ModelManager::GetInstance()->DoneReadOSDBuffer(DISPLAY_USAGE_LEFT);
        return false;
    }
    ModelManager::GetInstance()->DoneReadOSDBuffer(DISPLAY_USAGE_LEFT);
    osd_frame_info = ModelManager::GetInstance()->GetOSDReadableBuffer(DISPLAY_USAGE_RIGHT);
    if (!DispatcherWrapper::GetInstance()->SendOverlayFrame(DISPLAY_USAGE_RIGHT, osd_frame_info.get()))
    {
        ModelManager::GetInstance()->DoneReadOSDBuffer(DISPLAY_USAGE_RIGHT);
        return false;
    }
    ModelManager::GetInstance()->DoneReadOSDBuffer(DISPLAY_USAGE_RIGHT);

    if (p_dm->debug_log.osd_submit)
    {
        HERON_LOG_DEBUG("OSDSubmit: left:{} right:{} done", left_buffer_payload, right_buffer_payload);
    }
    return true;
}

void OSDCtrl::ClearCurrentBuffer()
{
    HERON_LOG_DEBUG("calling {}", __FUNCTION__);
    uint32_t osd_image_plane_size = OSD_FRAME_STRIDE * ModelManager::DEFAULT_OSD_QUAD_SIZE_PIXEL.y();
    OverlayFrameDataPtr left = ModelManager::GetInstance()->GetOSDReadableBuffer(DISPLAY_USAGE_LEFT);
    if (left)
    {
        std::memset((void *)left->data_data, 0, osd_image_plane_size * 4);
    }
    else
    {
        HERON_LOG_WARN("left osd buffer is null");
    }
    ModelManager::GetInstance()->DoneReadOSDBuffer(DISPLAY_USAGE_LEFT);

    OverlayFrameDataPtr right = ModelManager::GetInstance()->GetOSDReadableBuffer(DISPLAY_USAGE_RIGHT);
    if (right)
    {
        std::memset((void *)right->data_data, 0, osd_image_plane_size * 4);
    }
    else
    {
        HERON_LOG_WARN("right osd buffer is null");
    }
    ModelManager::GetInstance()->DoneReadOSDBuffer(DISPLAY_USAGE_RIGHT);
}