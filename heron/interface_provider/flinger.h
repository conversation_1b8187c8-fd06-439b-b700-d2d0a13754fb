#pragma once

#include <heron/util/types.h>

#include <framework/util/singleton.h>

namespace heron::interface_provider
{
    class FlingerInterface : public framework::util::Singleton<FlingerInterface>
    {
    public:
        void GetInterfaceInstance(void *interfaces);

        void RegisterLifecycleProvider(
            const char *plugin_id,
            const char *plugin_version,
            const void *provider,
            uint32_t provider_size);
        bool RegisterProvider(
            const void *provider,
            uint32_t provider_size);
        bool RegisterSpaceScreenProvider(
            const void *provider,
            uint32_t provider_size);
        bool GetHeadPose(
            PerceptionType perception_type,
            uint64_t hmd_time_nanos,
            TrackingPoseType pose_type,
            Transform *out_head_pose);
        bool SetPoseGuid(
            GUID pose_guid);
        bool GetDisplayDuty(
            int32_t *out_display_duty);
        /*

            通过解析dp中是否埋数据得知是否host有nebula在运行
            若有app在运行则是NR_SCENE_MODE_WITH_NEBULA
            否则是纯投屏NR_SCENE_MODE_SPACE_SCREEN

        */
        bool SetSceneMode(SceneMode scene_mode);
        /*

            计算最优的虚拟屏尺寸
            虚拟屏为quad
            输入：
            left_screen_roi：左屏的可视范围，单位为像素，为左屏内的一个矩形。屏幕左上角点为原点(0, 0), 屏幕右下角点为(screen_width, screen_height)。
            right_screen_roi：右屏的可视范围，单位为像素，为右屏内的一个矩形。屏幕左上角点为原点(0, 0), 屏幕右下角点为(screen_width, screen_height)。
            dp_resolution：需要显示在quad上的dp buffer的分辨率
            quad_distance：(是正数)虚拟屏在center camera坐标系下的深度值，单位是米。quad默认垂直于center camera的z轴，quad的x，y轴与center camera坐标系的x，y轴平行
            输出：
            out_quad_rect：虚拟屏的大小，单位是米。该虚拟屏上的点(0, 0, quad_distance)为center camera的视线与该屏幕的交点。允许虚拟屏不以点(0, 0, quad_distance)中心对称。

        */
        bool CalcuQuadSize(
            const Rectf *left_screen_roi,
            const Rectf *right_screen_roi,
            const Vector2i *dp_resolution,
            float quad_distance,
            Rectf *out_quad_rect);
        /*

            黑白编码
            输入：
            input_buffer_size：输入数据的大小
            input_buffer_data：输入数据
            input_buffer_format：输入数据的格式
            输入输出：
            output_buffer_size：当input_buffer_data和output_buffer_data为nullptr，为输出变量，用于计算输出数据所需的大小
            当input_buffer_data和output_buffer_data不为nullptr，为输入变量，用于指定输入数据的大小
            输出：
            output_buffer_data：输出数据

        */
        bool BWDecodeBuffer(
            ImageFormat input_buffer_format,
            uint32_t input_buffer_size,
            const char *input_buffer_data,
            uint32_t *output_buffer_size,
            char *output_buffer_data);
        /*

            /// 设置线程的 Fifo 优先级

        */
        bool SetMiscScheduler(
            int32_t pid,
            MiscSchedPolicy policy,
            int32_t priority);

        bool RgbCameraIsOccupied(bool *occupied);

        bool GetDevicePose(
            PerceptionType perception_type,
            uint64_t hmd_time_nanos,
            TrackingPoseType type,
            Transform *out_transform);

        bool PerceptionRecenter();

        bool GetDpEdid(Edid *out_dp_edid);

        bool GetDpConfig(DpConfig *dp_config);
        bool ResetDpDisplay(const DpConfig *dp_config, const DpDisplayConfig *dsplay_config, GlassesMode glasses_mode);

        bool GenDepthShiftedImage(Component src_component, const DpFrameData *src_image,
                                  Component dst_component, const DpFrameData *out_dst_image, bool *hold_src_data);

        bool GetGlassesMode(GlassesMode *out_glasses_mode);

        bool UpdateIPD(float ipd);

        bool GetHandData(uint64_t hmd_time_nanos, HandData out_hand_data[], uint32_t *out_hand_num);
    };

} // namespace heron::interface_provider