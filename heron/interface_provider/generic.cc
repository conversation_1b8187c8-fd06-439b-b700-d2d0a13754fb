#include <heron/interface_provider/generic.h>
#include <heron/interface_provider/common_macro.h>
#include <heron/interface/include/common/nr_plugin_generic.h>
#include <heron/util/nr_type_converter.h>
#include <heron/util/log.h>

#include <framework/util/plugin_util.h>

extern NRPluginHandle heron_g_handle;

static std::unique_ptr<NRGenericInterface> s_interface = nullptr;
namespace heron::interface_provider
{
    void GenericInterface::GetInterfaceInstance(void *interfaces)
    {
        s_interface = framework::util::GetInterface<NRGenericInterface>(static_cast<NRInterfaces *>(interfaces));
        if (!s_interface)
        {
            fprintf(stderr, "error on plugin load: get %s fail. going to abort", "NRGenericInterface");
            usleep(500 * 1000);
            std::abort();
        }
    }

    bool GenericInterface::GetGlobalConfig(const char **data, uint32_t *size)
    {
        CALL_INTERFACE_API(NRGenericInterface, GetGlobalConfig, false, data, size);
        return true;
    }

    bool GenericInterface::GetDeviceConfig(const char **data, uint32_t *size)
    {
        CALL_INTERFACE_API(NRGenericInterface, GetDeviceConfig, true, data, size);
        return true;
    }

    bool GenericInterface::GetAppDefaultConfig(const char **data, uint32_t *size)
    {
        CALL_INTERFACE_API(NRGenericInterface, GetAppDefaultConfig, true, data, size);
        return true;
    }

    bool GenericInterface::GetDeviceDefaultConfig(const char **data, uint32_t *size)
    {
        CALL_INTERFACE_API(NRGenericInterface, GetDeviceDefaultConfig, true, data, size);
        return true;
    }

    bool GenericInterface::GetDisplayResolutionInfo(ResolutionInfo *display_resolution)
    {
        NRResolutionInfo nr_display_resolution;
        CALL_INTERFACE_API(NRGenericInterface, GetDisplayResolutionInfo, true, &nr_display_resolution);
        ConvertToResolutionInfo(*display_resolution, nr_display_resolution);
        return true;
    }

    bool GenericInterface::GetLogCallback(void **log_callback)
    {
        CALL_INTERFACE_API(NRGenericInterface, GetLogCallback, true, log_callback);
        return true;
    }

} // namespace heron::interface_provider