#include <heron/interface_provider/flinger.h>
#include <heron/interface_provider/common_macro.h>

#include <heron/interface/include/flinger/nr_plugin_flinger.h>
#include <heron/util/nr_type_converter.h>
#include <heron/util/log.h>
#include <heron/util/task_queue.h>

#include <framework/util/plugin_util.h>

extern NRPluginHandle heron_g_handle;

static std::unique_ptr<NRFlingerInterface> s_interface = nullptr;

const void *GetFlingerProvider(uint32_t &size);
static NRFlingerProvider *s_flinger_provider = nullptr;
namespace heron::interface_provider
{
    void FlingerInterface::GetInterfaceInstance(void *interfaces)
    {
        s_interface = framework::util::GetInterface<NRFlingerInterface>(static_cast<NRInterfaces *>(interfaces));
        if (!s_interface)
        {
            fprintf(stderr, "error on plugin load: get %s fail. going to abort", "NRFlingerInterface");
            usleep(500 * 1000);
            std::abort();
        }
    }

    void FlingerInterface::RegisterLifecycleProvider(
        const char *plugin_id,
        const char *plugin_version,
        const void *provider,
        uint32_t provider_size)
    {
        s_interface->RegisterLifecycleProvider(plugin_id, plugin_version, static_cast<const NRPluginLifecycleProvider *>(provider), provider_size);
    }

    bool FlingerInterface::RegisterProvider(
        const void *provider,
        uint32_t provider_size)
    {
        CALL_INTERFACE_API(NRFlingerInterface, RegisterProvider, true, static_cast<const NRFlingerProvider *>(provider), provider_size);
        return true;
    }

    bool FlingerInterface::RegisterSpaceScreenProvider(
        const void *provider,
        uint32_t provider_size)
    {
        CALL_INTERFACE_API(NRFlingerInterface, RegisterSpaceScreenProvider, true, static_cast<const NRSpaceScreenProvider *>(provider), provider_size);
        return true;
    }

    // XXX: looking at direction (0,0,-1) to reduce GDC memory access
    bool FlingerInterface::GetHeadPose(
        PerceptionType perception_type,
        uint64_t hmd_time_nanos,
        TrackingPoseType pose_type,
        Transform *out_head_pose)
    {
        NRTransform nr_head_pose;
        CALL_INTERFACE_API_NO_RETURN_ON_ERROR(NRFlingerInterface, GetHeadPose, false, (NRPerceptionType)perception_type, hmd_time_nanos, (NRTrackingPoseType)pose_type, &nr_head_pose);
        if (ret != NR_PLUGIN_RESULT_SUCCESS)
        {
            out_head_pose->rotation = Quatf(0, 0, 1, 0); // (w, x, y, z)
            out_head_pose->position = Vector3f(0, 0, 0);
            return false;
        }
        NRTransformToTransform(*out_head_pose, nr_head_pose);
        return true;
    }
    bool FlingerInterface::SetPoseGuid(GUID pose_guid)
    {
        NRGUID nr_guid{pose_guid.high, pose_guid.low};
        CALL_INTERFACE_API(NRFlingerInterface, SetPoseGuid, true, nr_guid);
        return true;
    }

    bool FlingerInterface::GetDisplayDuty(int32_t *out_display_duty)
    {
        CALL_INTERFACE_API(NRFlingerInterface, GetDisplayDuty, true, out_display_duty);
        return true;
    }
    /*

        通过解析dp中是否埋数据得知是否host有nebula在运行
        若有app在运行则是NR_SCENE_MODE_WITH_NEBULA
        否则是纯投屏NR_SCENE_MODE_SPACE_SCREEN

    */
    bool FlingerInterface::SetSceneMode(SceneMode scene_mode)
    {
        CALL_INTERFACE_API(NRFlingerInterface, SetSceneMode, true, (NRSceneMode)scene_mode);
        return true;
    }
    /*
        计算最优的虚拟屏尺寸
        虚拟屏为quad
        输入：
        left_screen_roi：左屏的可视范围，单位为像素，为左屏内的一个矩形。屏幕左上角点为原点(0, 0), 屏幕右下角点为(screen_width, screen_height)。
        right_screen_roi：右屏的可视范围，单位为像素，为右屏内的一个矩形。屏幕左上角点为原点(0, 0), 屏幕右下角点为(screen_width, screen_height)。
        dp_resolution：需要显示在quad上的dp buffer的分辨率
        quad_distance：(是正数)虚拟屏在center camera坐标系下的深度值，单位是米。quad默认垂直于center camera的z轴，quad的x，y轴与center camera坐标系的x，y轴平行
        输出：
        out_quad_rect：虚拟屏的大小，单位是米。该虚拟屏上的点(0, 0, quad_distance)为center camera的视线与该屏幕的交点。允许虚拟屏不以点(0, 0, quad_distance)中心对称。

    */
    bool FlingerInterface::CalcuQuadSize(
        const Rectf *left_screen_roi,
        const Rectf *right_screen_roi,
        const Vector2i *dp_resolution,
        float quad_distance,
        Rectf *out_quad_rect)
    {
        NRRectf nr_left_screen_roi, nr_right_screen_roi, nr_out_quad_rect;
        NRSize2i nr_dp_resolution;
        ConvertToNRRectf(nr_left_screen_roi, *left_screen_roi);
        ConvertToNRRectf(nr_right_screen_roi, *right_screen_roi);
        ConvertToNRSize2i(nr_dp_resolution, *dp_resolution);
        CALL_INTERFACE_API(NRFlingerInterface, CalcuQuadSize, true, &nr_left_screen_roi, &nr_right_screen_roi, &nr_dp_resolution, quad_distance, &nr_out_quad_rect);
        ConvertToRectf(*out_quad_rect, nr_out_quad_rect);
        return true;
    }

    bool FlingerInterface::BWDecodeBuffer(
        ImageFormat input_buffer_format,
        uint32_t input_buffer_size,
        const char *input_buffer_data,
        uint32_t *output_buffer_size,
        char *output_buffer_data)
    {
        CALL_INTERFACE_API(NRFlingerInterface, BWDecodeBuffer, false,
                           (NRImageFormat)input_buffer_format,
                           input_buffer_size,
                           input_buffer_data,
                           output_buffer_size,
                           output_buffer_data);
        return true;
    }
    /*

        /// 设置线程的 Fifo 优先级

    */
    bool FlingerInterface::SetMiscScheduler(
        int32_t pid,
        MiscSchedPolicy policy,
        int32_t priority)
    {
        CALL_INTERFACE_API(NRFlingerInterface, SetMiscScheduler, true, pid, (NRMiscSchedPolicy)policy, priority);
        return true;
    }

    bool FlingerInterface::RgbCameraIsOccupied(bool *occupied)
    {
        CALL_INTERFACE_API(NRFlingerInterface, RgbCameraIsOccupied, true, occupied);
        return true;
    }

    bool FlingerInterface::GetDevicePose(
        PerceptionType perception_type,
        uint64_t hmd_time_nanos,
        TrackingPoseType type,
        Transform *out_head_pose)
    {
        NRDevicePose nr_device_pose;
        CALL_INTERFACE_API_NO_RETURN_ON_ERROR(NRFlingerInterface, GetDevicePose, false, (NRPerceptionType)perception_type, hmd_time_nanos, (NRTrackingPoseType)type, &nr_device_pose);
        if (ret != NR_PLUGIN_RESULT_SUCCESS)
        {
            out_head_pose->rotation = Quatf(0, 0, 1, 0); // (w, x, y, z)
            out_head_pose->position = Vector3f(0, 0, 0);
            // HERON_LOG_TRACE("return: {} reason: {}", ret, nr_device_pose.tracking_reason);
            return false;
        }
        NRTransformToTransform(*out_head_pose, nr_device_pose.transform);
        return true;
    }

    bool FlingerInterface::PerceptionRecenter()
    {
        CALL_INTERFACE_API(NRFlingerInterface, PerceptionRecenter, true);
        return true;
    }
    bool FlingerInterface::GetDpEdid(Edid *out_edid)
    {
        NREdid nr_edid;
        CALL_INTERFACE_API(NRFlingerInterface, GetDpEdid, true, &nr_edid);
        *out_edid = (Edid)nr_edid;
        return true;
    }

    bool FlingerInterface::GetDpConfig(DpConfig *dp_config)
    {
        NRDpConfig nr_dp_config;
        CALL_INTERFACE_API(NRFlingerInterface, GetDpConfig, true, &nr_dp_config);
        ConvertToDpConfig(*dp_config, nr_dp_config);
        return true;
    }

    bool FlingerInterface::ResetDpDisplay(const DpConfig *dp_config, const DpDisplayConfig *display_config, GlassesMode glasses_mode)
    {
        NRDpConfig nr_dp_config;
        NRDpDisplayConfig nr_dp_display_config;
        ConvertToNRDpConfig(nr_dp_config, *dp_config);
        ConvertToNRDpDisplayConfig(nr_dp_display_config, *display_config);
        CALL_INTERFACE_API(NRFlingerInterface, ResetDpDisplay, true, &nr_dp_config, &nr_dp_display_config, (NRGlassesMode)glasses_mode);
        return true;
    }

    static TaskQueue s_gen_shifted_frame_task_queue("shift_frame_task_queue", 3);
    static uint32_t s_gen_shifted_frame_count = 0;
    static bool s_task_queue_started = false;

    static void GenDepthShiftedImageStub(const NRDpFrameData *src_image)
    {
        const NRDpFrameData *out_dst_image;
        if (s_flinger_provider->AcquireWritableDpFrameData(heron_g_handle, &out_dst_image) != NR_PLUGIN_RESULT_SUCCESS)
        {
            HERON_LOG_DEBUG("AcquireWritableDpFrameData failed");
            return;
        }
        uint32_t height = src_image->height;
        uint32_t width = src_image->width;
        // HERON_LOG_DEBUG("y_width:{} y_height:{} y_stride:{}", width, height, src_image->strides.x);
        for (uint32_t i = 0; i < height; i++)
            memcpy(((char *)out_dst_image->data.x) + i * out_dst_image->strides.x, ((char *)src_image->data.x) + i * src_image->strides.x, width);
        height /= 2;
        width /= 2;
        // HERON_LOG_DEBUG("uv_width:{} uv_height:{} uv_stride:{}", width, height, src_image->strides.y);
        for (uint32_t i = 0; i < height; i++)
            memcpy(((char *)out_dst_image->data.y) + i * out_dst_image->strides.y, ((char *)src_image->data.z) + i * src_image->strides.z, width);
        for (uint32_t i = 0; i < height; i++)
            memcpy(((char *)out_dst_image->data.z) + i * out_dst_image->strides.z, ((char *)src_image->data.y) + i * src_image->strides.y, width);
        s_flinger_provider->SubmitStereoDpFrameData(heron_g_handle, out_dst_image, src_image);
    }

    static bool MaybeGenDepthShiftedImageStub(const NRDpFrameData *src_image)
    {
        HERON_LOG_DEBUG("MaybeGenDepthShiftedImageStub count:{}", s_gen_shifted_frame_count);
        if (s_gen_shifted_frame_count % 10 == 0)
        {
            NRDpFrameData tmp_src_image = *src_image;
            s_gen_shifted_frame_task_queue.AddTask([tmp_src_image]()
                                                   { GenDepthShiftedImageStub(&tmp_src_image); });
            ++s_gen_shifted_frame_count;
            return true;
        }
        ++s_gen_shifted_frame_count;
        return false;
    }

    static void PrintNRDpFrameData(const NRDpFrameData *dp_frame_data)
    {
        HERON_LOG_DEBUG("width:{} height:{} format:{} data:{},{},{} strides:{},{},{} strides_addr_offset:{}",
                        dp_frame_data->width, dp_frame_data->height, dp_frame_data->pixel_format,
                        dp_frame_data->data.x, dp_frame_data->data.y, dp_frame_data->data.z,
                        dp_frame_data->strides.x, dp_frame_data->strides.y, dp_frame_data->strides.z,
                        (size_t)&dp_frame_data->strides - (size_t)dp_frame_data);
    }

    bool FlingerInterface::GenDepthShiftedImage(Component src_component, const DpFrameData *src_image,
                                                Component dst_component, const DpFrameData *out_dst_image, bool *hold_src_data)
    {
        NRDpFrameData nr_src_image, nr_dst_image;
        ConvertToNRDpFrameData(nr_src_image, *src_image);
        if (out_dst_image)
            ConvertToNRDpFrameData(nr_dst_image, *out_dst_image);
        if (s_interface->GenDepthShiftedImage)
        {
            // PrintNRDpFrameData(&nr_src_image);
            if (false)
            {
                if (!s_task_queue_started)
                {
                    s_gen_shifted_frame_task_queue.Start();
                    uint32_t provider_size;
                    s_flinger_provider = (NRFlingerProvider *)GetFlingerProvider(provider_size);
                    s_task_queue_started = true;
                }
                *hold_src_data = MaybeGenDepthShiftedImageStub(&nr_src_image);
            }
            else
            {
                NRPluginResult ret = s_interface->GenDepthShiftedImage(heron_g_handle, (NRComponent)src_component, &nr_src_image, (NRComponent)dst_component, &nr_dst_image, hold_src_data);
                if (ret != NR_PLUGIN_RESULT_SUCCESS)
                {
                    HERON_LOG_WARN("GenDepthShiftedImage failed. ret: {} addr: {}", ret, (void *)s_interface->GenDepthShiftedImage);
                    return false;
                }
            }
        }
        return true;
    }

    bool FlingerInterface::GetGlassesMode(GlassesMode *out_glasses_mode)
    {
        NRGlassesMode nr_glasses_mode = NR_GLASSES_MODE_UNKNOWN;
        CALL_INTERFACE_API(NRFlingerInterface, GetGlassesMode, true, &nr_glasses_mode);
        *out_glasses_mode = (GlassesMode)nr_glasses_mode;
        return true;
    }

    bool FlingerInterface::UpdateIPD(float ipd)
    {
        CALL_INTERFACE_API(NRFlingerInterface, UpdateIPD, true, ipd);
        return true;
    }

    bool FlingerInterface::GetHandData(uint64_t hmd_time_nanos, HandData out_hand_data[], uint32_t *out_hand_num)
    {
        const NRHandData *nr_hand_data;
        CALL_INTERFACE_API(NRFlingerInterface, GetHandData, true, hmd_time_nanos, &nr_hand_data, out_hand_num);
        for (uint32_t i = 0; i < *out_hand_num; i++)
        {
            if (nr_hand_data[i].is_tracked)
                ConvertToHandData(out_hand_data[i], nr_hand_data[i]);
            else
                out_hand_data[i].hand_type = HAND_TYPE_UNKNOWN;
        }
        return true;
    }
} // namespace heron::interface_provider