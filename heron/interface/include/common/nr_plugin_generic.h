#pragma once

#include "nr_plugin_interface.h"
#include "nr_plugin_types.h"
#include "nr_plugin_glasses_types.h"
NR_DECLARE_INTERFACE(NRGenericInterface) {

    NRPluginResult(NR_INTERFACE_API *GetSDKVersion)(
        NRVersion * version
    );

    NRPluginResult(NR_INTERFACE_API *GetActivityInfo)(
        void ** java_vm,
        void ** context,
        void ** class_loader
    );

    NRPluginResult(NR_INTERFACE_API *GetPluginConfig)(
        const char ** data,
        uint32_t * data_size
    );

    NRPluginResult(NR_INTERFACE_API *GetDeviceConfig)(
        NRPluginHandle handle,
        const char ** data,
        uint32_t * data_size
    );

    NRPluginResult(NR_INTERFACE_API *GetDeviceMiscConfig)(
        NRPluginHandle handle,
        const char ** data,
        uint32_t * data_size
    );

    NRPluginResult(NR_INTERFACE_API *GetGlobalConfig)(
        NRPluginHandle handle,
        const char ** data,
        uint32_t * data_size
    );

    NRPluginResult(NR_INTERFACE_API *GetNetworkType)(
        NRPluginHandle handle,
        NRNetworkType * network_type
    );

    NRPluginResult(NR_INTERFACE_API *GetDeviceType)(
        NRPluginHandle handle,
        NRDeviceType * device_type
    );

    NRPluginResult(NR_INTERFACE_API *GetDynamicLibraryPath)(
        NRPluginHandle handle,
        const char ** out_path,
        uint32_t * path_size
    );

    NRPluginResult(NR_INTERFACE_API *GetChannelIdentifier)(
        NRPluginHandle handle,
        const char ** channel_identifier,
        uint32_t * channel_identifier_size
    );

    NRPluginResult(NR_INTERFACE_API *SetThreadPriority)(
        NRPluginHandle handle,
        int32_t tid,
        int32_t policy,
        int32_t priority
    );

    NRPluginResult(NR_INTERFACE_API *GetPropertyFlag)(
        NRPluginHandle handle,
        NRProperty property,
        int32_t * flag
    );

    NRPluginResult(NR_INTERFACE_API *UpdateMetricsui64)(
        NRPluginHandle handle,
        NRMetricsType metrics_type,
        uint64_t metrics_data
    );

    NRPluginResult(NR_INTERFACE_API *UpdateMetricsui)(
        NRPluginHandle handle,
        NRMetricsType metrics_type,
        uint32_t metrics_data
    );

    NRPluginResult(NR_INTERFACE_API *GetPluginDebugConfig)(
        const char ** data,
        uint32_t * data_size
    );

    NRPluginResult(NR_INTERFACE_API *GetWorkingMode)(
        NRPluginHandle handle,
        NRWorkingMode * working_mode
    );

    NRPluginResult(NR_INTERFACE_API *GetDisplayInfo)(
        NRPluginHandle handle,
        NRDisplayInfo * out_display_info
    );

    NRPluginResult(NR_INTERFACE_API *GetDeviceConnectMode)(
        NRDeviceConnectMode * connect_mode
    );
	/*
		
		获取pilot_default_config.json数据内容
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetAppDefaultConfig)(
        NRPluginHandle handle,
        const char ** data,
        uint32_t * data_size
    );
	/*
		
		获取设备相关的pilot_default_config_{device_name}.json数据内容
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetDeviceDefaultConfig)(
        NRPluginHandle handle,
        const char ** data,
        uint32_t * data_size
    );
	/*
		
		/// @brief 获取oled的分辨率和刷新率信息
		/// @return 结果
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetDisplayResolutionInfo)(
        NRPluginHandle handle,
        NRResolutionInfo * display_resolution
    );
	/*
		
		clock_sync_time = to_device_time - from_device_time 
		
	*/
    NRPluginResult(NR_INTERFACE_API *GetClockSync)(
        NRPluginHandle handle,
        NRClockDeviceType from_device_type,
        NRClockDeviceType to_device_type,
        int64_t * clock_sync_time
    );

    NRPluginResult(NR_INTERFACE_API *GetLogCallback)(
        NRPluginHandle handle,
        void ** log_callback
    );

    NRPluginResult(NR_INTERFACE_API *GetHostType)(
        NRPluginHandle handle,
        NRMiscHostType * out_host_type
    );
};

NR_REGISTER_INTERFACE_GUID(0xEA9C4E1B179EF26CULL, 0x99E82B632499DC03ULL,
                            NRGenericInterface)

