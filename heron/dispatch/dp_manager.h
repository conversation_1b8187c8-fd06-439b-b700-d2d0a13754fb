#pragma once

#include <heron/model/model.h>
#include <heron/util/ringbuffer.h>
#include <heron/util/counter.h>
#include <heron/util/task_queue.h>

#include <framework/util/singleton.h>

#include <thread>

typedef struct NRDpFrameData NRDpFrameData;
namespace heron::dispatch
{

    class DpManager : public framework::util::Singleton<DpManager>
    {
    public:
        enum
        {
            VI_DEV = 0,
            VI_PIPE = VI_DEV,
            VI_CHN = 0,
        };
        enum
        {
            FRAME_BUFFER_COUNT = 16,
        };
        using FramePool = std::vector<std::unique_ptr<NRDpFrameData>>;
        using FramePtr = NRDpFrameData *;
        using FramePtrVec = std::vector<FramePtr>;

    public:
        DpManager();
        ~DpManager();
        void StartReceiveThread();
        void StopReceiveThread();

        bool AllocShiftedFrame(uint32_t width, uint32_t height);
        bool AcquireWritableDpFrameData(const DpFrameData **dst_frame_data);
        bool SubmitStereoDpFrameData(const DpFrameData *frame_data_dst, const DpFrameData *frame_data_src);

    private:
        void Init();
        void Clear();
        void ReceiveFrame();
        void ReceiveShiftedFrameSrc();
        void MaybeConfigureGDC(model::FrameInfo *frame_info);

    private:
        FramePtr AllocFrame();
        void FreeFrame(FramePtr frame);

        bool AllocShiftedFrameInternal(uint32_t width, uint32_t height, DpFrameData &dp_frame_data, const char *name);
        void FreeShiftedFrame(DpFrameData &dp_frame_data);

    private:
        std::thread thread_;
        std::atomic<bool> running_{false};

    private:
        FramePool frames_;
        FramePtrVec fresh_frames_;
        BoundedBlockingQueue<FramePtr> fresh_frame_queue_; // used for "GenDepthShiftedImage Mode"

        TaskQueue release_frame_task_queue_;
        DpFrameData *writable_shifted_frame_;
        uint32_t current_shifted_frame_idx_{0};
        std::vector<DpFrameData> shifted_frame_pool_;

    public:
        void SetExpectedFps(float fps) { fps_counter_.SetExpectedCallCountsPerSecond(fps); }
        void ResetConsecutiveValidFrameCount() { consecutive_valid_frame_count_ = 0; }

    private:
        int64_t consecutive_valid_frame_count_ = 0;

        const uint32_t DP_FPS_SAMPLE_SIZE = 100;
        CallCounter fps_counter_{"dp", DP_FPS_SAMPLE_SIZE, 90.0f, 5.0f};
        CallCounter shifted_fps_counter_{"shifted", DP_FPS_SAMPLE_SIZE, 0.0f, std::numeric_limits<float>::max()};
        CallCounter hold_src_fps_counter_{"hold_src", DP_FPS_SAMPLE_SIZE, 0.0f, std::numeric_limits<float>::max()};
        Average recv_frame_cost_{"recv_frame_cost"};
        Average flush_shifted_image_cache_{"flush_cache"};
    };

} // namespace heron::dispatch
