#include <heron/env/system_config.h>
#include <heron/dispatch/dispatcher.h>
#include <heron/util/debug.h>
#include <heron/util/log.h>

#include <framework/util/dlutil.h>

using namespace heron::dispatch;
using namespace framework::util;

#define LOAD_XR_BSP_FUNCTION(__FUNCTION_NAME__)                                                            \
    __FUNCTION_NAME__ = (PFN_##__FUNCTION_NAME__)LoadFunction(dl_handle, #__FUNCTION_NAME__, require_log); \
    if (__FUNCTION_NAME__)                                                                                 \
    {                                                                                                      \
        HERON_LOG_INFO("Load function {} success", #__FUNCTION_NAME__);                                    \
    }                                                                                                      \
    else                                                                                                   \
    {                                                                                                      \
        HERON_LOG_ERROR("going to abort!");                                                                \
        usleep(500 * 1000);                                                                                \
        std::abort();                                                                                      \
    }

Dispatcher::Dispatcher()
{
    // HERON_LOG_TRACE("Dispatcher Constructor");
}

// TODO: change the log level
void Dispatcher::LoadLibraries()
{
    bool require_log = true;
    {
#ifdef HERON_SYSTEM_MACOS
        LibraryHandle dl_handle = LoadLibrary("./94_board/libar94_api_stub.dylib", require_log);
        if (!dl_handle)
        {
            HERON_LOG_ERROR("libar94_api_stub.dylib load fail");
            usleep(500 * 1000);
            std::abort();
        }

#elif defined(HERON_SYSTEM_XRLINUX)
        bool using_stub_library = false;
        LibraryHandle dl_handle = LoadLibrary("libdisplayservice.so", require_log);
        if (!dl_handle)
        {
            HERON_LOG_ERROR("libdisplayservice.so load fail");
            usleep(500 * 1000);
            std::abort();
        }
        std::string function_name = "";

        LOAD_XR_BSP_FUNCTION(NRDpGetFrame);
        LOAD_XR_BSP_FUNCTION(NRDpReleaseFrame);
        LOAD_XR_BSP_FUNCTION(NRDpResizeFrame);

        LOAD_XR_BSP_FUNCTION(NRMmzAlloc);
        LOAD_XR_BSP_FUNCTION(NRMmzFree);
        LOAD_XR_BSP_FUNCTION(NRGdcInit);
        LOAD_XR_BSP_FUNCTION(NRGdcProcess);
        LOAD_XR_BSP_FUNCTION(NRGdcProcessDiscardOnBusy);

        LOAD_XR_BSP_FUNCTION(NRDisplayConfigService);
        LOAD_XR_BSP_FUNCTION(NRDisplaySetCallback);
        LOAD_XR_BSP_FUNCTION(NRDisplayStartOSDRender);
        LOAD_XR_BSP_FUNCTION(NRDisplayStopOSDRender);
        LOAD_XR_BSP_FUNCTION(NRDisplayAllocOverlayFrame);
        LOAD_XR_BSP_FUNCTION(NRDisplayDeallocOverlayFrame);
        LOAD_XR_BSP_FUNCTION(NRDisplaySendOverlayFrame);

        LOAD_XR_BSP_FUNCTION(NRDisplaySetScreenEnableBsp);

        dl_handle = LoadLibrary("libar94_api.so", false);
        if (!dl_handle)
            return;
        HERON_LOG_DEBUG("load libar94_api.so success");
        LOAD_XR_BSP_FUNCTION(NRLoadAR94APIs);
        LOAD_XR_BSP_FUNCTION(NRInitBoardContext);
        LOAD_XR_BSP_FUNCTION(NRLocalGdcInit);
        LOAD_XR_BSP_FUNCTION(NRLocalGdcProcess);
        LOAD_XR_BSP_FUNCTION(NRLocalGdcRenderToMmz);
        LOAD_XR_BSP_FUNCTION(NRLocalDisplayStartOSDRender);
        LOAD_XR_BSP_FUNCTION(NRLocalDisplayStopOSDRender);
        LOAD_XR_BSP_FUNCTION(NRLocalUpdateOSDPupilMask);
        LOAD_XR_BSP_FUNCTION(NRLocalDisplayAllocOverlayFrame);
        LOAD_XR_BSP_FUNCTION(NRLocalDisplayDeallocOverlayFrame);
        LOAD_XR_BSP_FUNCTION(NRLocalDisplaySendOverlayFrame);
        LOAD_XR_BSP_FUNCTION(XR_ar_hal_sys_mmz_alloc);
        LOAD_XR_BSP_FUNCTION(XR_ar_hal_sys_mmz_free);
        LOAD_XR_BSP_FUNCTION(XR_ar_hal_sys_mmz_alloc_cached);
        LOAD_XR_BSP_FUNCTION(XR_ar_hal_sys_mmz_flush_cache);
#else
        HERON_LOG_ERROR("current system not supported yet.");
        usleep(500 * 1000);
        std::abort();
#endif
    }
}
