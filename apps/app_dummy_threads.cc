/**
 * @file app_dummy_thread.cc
 * @brief This file contains main function to do unit test
 *
 * This program create dummy threads. For testing behavior
 * when thread kill on xrlinux system
 *
 * <AUTHOR> <EMAIL>
 * @date 08/15/2025
 */

#include <signal.h>
#include <pthread.h>
#include <unistd.h>
#include <thread>
#include <iostream>
#include <vector>
#include <atomic>
#include <sys/syscall.h> // 定义 SYS_gettid 宏

// 每个线程的退出标志和信号配置
struct ThreadData
{
    pthread_t tid;                      // 线程ID
    pid_t tid2;                         // 线程ID
    std::string name;                   // 线程名
    std::atomic<bool> exit_flag{false}; // 线程退出标志
    int signal_num;                     // 分配给线程的信号（如 SIGRTMIN+0, SIGRTMIN+1）

    // 显式实现移动构造函数
    ThreadData(ThreadData &&other) noexcept : tid(other.tid),
                                              name(std::move(other.name)),
                                              exit_flag(other.exit_flag.load()),
                                              signal_num(other.signal_num) {}

    // 显式实现拷贝构造函数
    ThreadData(const ThreadData &other) : tid(other.tid),
                                          name(other.name),
                                          exit_flag(other.exit_flag.load()),
                                          signal_num(other.signal_num) {}

    // 默认构造函数
    ThreadData() = default;
};

// 全局线程数据
std::vector<ThreadData> threads;

// 信号处理函数（根据信号区分线程）
void handle_signal(int sig)
{
    for (auto &t : threads)
    {
        if (sig == t.signal_num)
        {
            t.exit_flag = true; // 标记对应线程退出
            break;
        }
    }
}

// 线程函数
void *thread_func(void *arg)
{
    ThreadData *data = static_cast<ThreadData *>(arg);
    data->tid2 = syscall(SYS_gettid);
    // 设置线程名
    if (!data->name.empty())
        pthread_setname_np(pthread_self(), data->name.substr(0, 15).c_str());

    // 注册信号处理（仅当前线程生效）
    struct sigaction sa;
    sa.sa_handler = handle_signal;
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = 0;
    sigaction(data->signal_num, &sa, nullptr);

    // 解除信号屏蔽
    sigset_t mask;
    sigemptyset(&mask);
    sigaddset(&mask, data->signal_num);
    pthread_sigmask(SIG_UNBLOCK, &mask, nullptr);

    printf("Thread %ld started (Signal: %d)\n", data->tid2, data->signal_num);

    // 循环检查退出标志
    while (!data->exit_flag)
    {
        printf("Thread %ld working...\n", data->tid2);
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }

    printf("Thread %ld exiting.\n", data->tid2);
    return nullptr;
}

int main()
{
    // 初始化两个线程，分配不同的信号
    threads.resize(2);
    threads[0].signal_num = SIGRTMIN + 0; // 线程1用 SIGRTMIN+0
    threads[0].name = "dummy_1";
    threads[1].signal_num = SIGRTMIN + 1; // 线程2用 SIGRTMIN+1
    threads[1].name = "dummy_2";

    // 创建线程
    for (auto &t : threads)
    {
        pthread_create(&t.tid, nullptr, thread_func, &t);
        usleep(100 * 1000);
    }

    // 等待所有线程退出
    for (auto &t : threads)
        pthread_join(t.tid, nullptr);

    return 0;
}