/**
 * @file board_config.h
 * <AUTHOR>
 * @brief 此头文件中定义了BoardConfig结构体
 * @version 0.1
 * @date 2023-07-28
 *
 * @copyright Copyright (c) 2023
 *
 */
#ifndef BOARD_CONFIG_H
#define BOARD_CONFIG_H

#include <cstring> //for memcpy

#include <hal_sys.h>
#include <hal_vb.h>
#include <mpi_gdc_api.h>
#include <mpi_vo.h>

#define MAX_WARP_FILE_ARRAY_SIZE 1000

#define TOTAL_BLOCK_CNT 34
#define BLOCK_LINE_CNT 32 // lines per block

namespace AR94
{

    /**
     * @brief BoardConfig 类包含
     * AR94板子工作在“典型模式”下所产生的所有“状态”和“数据“
     * 可分类为：
     * Vin相关
     * Vout相关
     * GDC相关
     * Frame相关
     */
    class BoardConfig
    {
    public:
        BoardConfig() {}

        SIZE_S stSize = {0};
        SIZE_S stSize_ch = {0};
        SIZE_S stDevSize = {1920, 1080}; // 语义是output display的size

        VB_CONFIG_S stVbConf = {0};

        AR_S32(*vo_subscribe_call_back)
        (VO_DEV dev_id, VO_SUBSCRIBE_INFO_S *sub_info); /*VO的订阅回调函数 */
        AR_S32 VoDev[2] = {0, 1};
        AR_S32 VoLayer[2] = {VO_LAYER_ID_VIDEO_0, VO_LAYER_ID_VIDEO_1};
        VO_PUB_ATTR_S stPubAttr[2] = {0};
        VO_VIDEO_LAYER_ATTR_S stLayerAttr[2] = {0};

        VO_VIDEO_LAYER_ATTR_S stOverLayer0Attr[2] = {0};
        AR_S32 VoOverLayer0[2] = {VO_LAYER_ID_OVERLAY_0_0, VO_LAYER_ID_OVERLAY_1_0};
        VO_CHN_ATTR_S stOverlay0ChnAttr[2] = {0};
        SIZE_S stOverLayer0DevSize;

        VO_VIDEO_LAYER_ATTR_S stOverLayer1Attr[2] = {0};
        AR_S32 VoOverLayer1[2] = {VO_LAYER_ID_OVERLAY_0_1, VO_LAYER_ID_OVERLAY_1_1};
        VO_CHN_ATTR_S stOverlay1ChnAttr[2] = {0};
        SIZE_S stOverLayer1DevSize;


        VO_IRQ_ATTR_S stIrqAttr[2] = {{IRQ_TYPE_VSYNC, 0}};
        VO_SUBSCRIBE_ATTR_S stSubAttr[2] = {0};

        AR_S32 sensor_mode = 0x80; // 1920x1080 mode

        int isp_fre = 300000000;
        int vif_fre = 300000000;
        int pcs_fre = 100000000;
        VI_DEV ViDev = 0;
        VI_PIPE ViPipe = ViDev;
        VI_CHN ViChn = 0;
        AR_S32 mipi_index = 0;
        AR_S8 s8I2cDev = 2;

        VIDEO_FRAME_INFO_S frame = {0};

        AR_GDC_ADV_TRANSFORM_S stParam[2] = {0};
        AR_GDC_ADV_LINE_PARAMS_S stLineParam[2] = {0};

        /**
         * @brief necessary configs for app
         */
        uint64_t oled_dp_time_offset = 0;

    public:
        void Stop(int entrance);
    };

    bool GetCurrentBoardConfig(BoardConfig &config);

} // AR94
#endif