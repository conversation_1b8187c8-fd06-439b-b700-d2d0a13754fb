#include <94_board.h>

#include <board_context/board_context.h>
#include <pipeline/vi_dp.h>
#include <pipeline/vo_dp.h>

#include <enums.h>
#include <log.h>

#include <atomic>

extern PFN_AR_MPI_SYS_Exit pfn_AR_MPI_SYS_Exit;
extern PFN_AR_MPI_VB_Exit pfn_AR_MPI_VB_Exit;
extern PFN_AR_MPI_VB_ExitModCommPool pfn_AR_MPI_VB_ExitModCommPool;

extern PFN_AR_MPI_VO_EnableChn pfn_AR_MPI_VO_EnableChn;
extern PFN_AR_MPI_VO_DisableChn pfn_AR_MPI_VO_DisableChn;
extern PFN_AR_MPI_VO_SetChnAttr pfn_AR_MPI_VO_SetChnAttr;
extern PFN_AR_MPI_VO_EnableVideoLayer pfn_AR_MPI_VO_EnableVideoLayer;
extern PFN_AR_MPI_VO_DisableVideoLayer pfn_AR_MPI_VO_DisableVideoLayer;
extern PFN_AR_MPI_VO_SetVideoLayerAttr pfn_AR_MPI_VO_SetVideoLayerAttr;
extern PFN_AR_MPI_VO_SetVideoLayerPos pfn_AR_MPI_VO_SetVideoLayerPos;
extern PFN_AR_MPI_VO_SendFrame pfn_AR_MPI_VO_SendFrame;
extern PFN_AR_MPI_VO_SetVideoLayerColorTable pfn_AR_MPI_VO_SetVideoLayerColorTable;

static AR_VOID SAMPLE_COMM_SYS_Exit(void)
{
    pfn_AR_MPI_SYS_Exit();
    pfn_AR_MPI_VB_ExitModCommPool(VB_UID_VDEC);
    pfn_AR_MPI_VB_Exit();
    return;
}

namespace app
{

    void BoardContext::RegisterDPUCallback(AR_S32 (*vo_subscribe_call_back)(VO_DEV dev_id, VO_SUBSCRIBE_INFO_S *sub_info))
    {
        board_config_.vo_subscribe_call_back = vo_subscribe_call_back;
    }

    bool BoardContext::Init()
    {
        BOARD_LOG_TRACE("BoardContext INITIALIZING!");
        bool result = false;
        do
        {
            if (!AR94::GetCurrentBoardConfig(board_config_))
                break;
            if (!genPipelineComponents())
                break;
            if (!src_provider_->Init(board_config_))
                break;
            if (!result_presenter_->Init(board_config_))
                break;
            result = true;
        } while (0);
        initOtherConfig();
        BOARD_LOG_TRACE("BoardContext INITIALIZED!");
        return result;
    }
    bool BoardContext::DeInit()
    {
        BOARD_LOG_TRACE("BoardContext DEINITIALIZING!");
        SAMPLE_COMM_SYS_Exit();
        BOARD_LOG_TRACE("BoardContext DEINITIALIZED!");
        return true;
    }

    void BoardContext::initOtherConfig()
    {
        board_config_.oled_dp_time_offset = 1;
    }

    bool BoardContext::genPipelineComponents()
    {
        BOARD_LOG_TRACE("BoardContext generating PipelineComponents.");
        src_provider_ = std::make_shared<VIDP>();
        result_presenter_ = std::make_shared<VODP>();
        return true;
    }

    bool BoardContext::Start()
    {
        BOARD_LOG_INFO("BoardContext STARTING!");
        bool result = false;
        do
        {
            if (!src_provider_->Start(board_config_))
                break;
            if (!result_presenter_->Start(board_config_))
                break;
            result = true;
        } while (0);
        BOARD_LOG_INFO("BoardContext STARTED!");
        return result;
    }

    void BoardContext::Stop()
    {
        BOARD_LOG_INFO("BoardContext STOPPIING!");
        result_presenter_->Stop(board_config_);
        src_provider_->Stop(board_config_);
        SAMPLE_COMM_SYS_Exit();
        BOARD_LOG_INFO("BoardContext STOPPED!");
    }

    static std::atomic<bool> s_overlay0_enabled[2]{false, false};
    bool BoardContext::EnableOverlay0(uint32_t vo_id, uint32_t start_x, uint32_t start_y, uint32_t width, uint32_t height)
    {
        if (s_overlay0_enabled[vo_id].load())
            return true;
        s_overlay0_enabled[vo_id].store(true);
        BOARD_LOG_INFO("BoardContext EnableOverlay0{} {}x{} at ({},{})!", vo_id, width, height, start_x, start_y);
        board_config_.stOverLayer0DevSize.u32Width = width;
        board_config_.stOverLayer0DevSize.u32Height = height;
        board_config_.stOverLayer0Attr[vo_id].bClusterMode = AR_FALSE;
        board_config_.stOverLayer0Attr[vo_id].bDoubleFrame = AR_FALSE;
        board_config_.stOverLayer0Attr[vo_id].enDstDynamicRange = DYNAMIC_RANGE_SDR8;
        board_config_.stOverLayer0Attr[vo_id].stDispRect.s32X = 0;
        board_config_.stOverLayer0Attr[vo_id].stDispRect.s32Y = 0;
        board_config_.stOverLayer0Attr[vo_id].stDispRect.u32Height = board_config_.stOverLayer0DevSize.u32Height;
        board_config_.stOverLayer0Attr[vo_id].stDispRect.u32Width = board_config_.stOverLayer0DevSize.u32Width;
        board_config_.stOverLayer0Attr[vo_id].stImageSize.u32Height = board_config_.stOverLayer0DevSize.u32Height;
        board_config_.stOverLayer0Attr[vo_id].stImageSize.u32Width = board_config_.stOverLayer0DevSize.u32Width;
        board_config_.stOverLayer0Attr[vo_id].enPixFormat = PIXEL_FORMAT_ARGB_4444;
        board_config_.stOverLayer0Attr[vo_id].u32DispFrmRt = 30;
        board_config_.stOverLayer0Attr[vo_id].u32Stride[0] = AR_ALIGN128(board_config_.stOverLayer0DevSize.u32Width * 2);
        board_config_.stOverLayer0Attr[vo_id].u32Stride[1] = 0;
        board_config_.stOverLayer0Attr[vo_id].u32Stride[2] = 0;
        board_config_.stOverLayer0Attr[vo_id].u32Stride[3] = 0;

        board_config_.stOverLayer0Attr[vo_id].memMode = VO_MEMORY_MODE_LOW;

        BOARD_LOG_INFO("BoardContext EnableOverlay 0");
        AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_SetVideoLayerAttr(board_config_.VoOverLayer0[vo_id], &board_config_.stOverLayer0Attr[vo_id]), "AR_MPI_VO_SetVideoLayerAttr");

        POINT_S stOverlay0Pos = {0};
        stOverlay0Pos.s32X = start_x;
        stOverlay0Pos.s32Y = start_y;
        BOARD_LOG_INFO("BoardContext EnableOverlay 1");
        AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_SetVideoLayerPos(board_config_.VoOverLayer0[vo_id], &stOverlay0Pos), "AR_MPI_VO_SetVideoLayerPos");

        /* ENABLE VO OVERLAYER0 */
        BOARD_LOG_INFO("BoardContext EnableOverlay 2");
        AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_EnableVideoLayer(board_config_.VoOverLayer0[vo_id]), "AR_MPI_VO_EnableVideoLayer");

        /* SET AND ENABLE VO CHN */
        memset(&board_config_.stOverlay0ChnAttr[vo_id], 0, sizeof(VO_CHN_ATTR_S));
        board_config_.stOverlay0ChnAttr[vo_id].bDeflicker = AR_FALSE;
        board_config_.stOverlay0ChnAttr[vo_id].u32Priority = 0;
        board_config_.stOverlay0ChnAttr[vo_id].stRect.s32X = 0;
        board_config_.stOverlay0ChnAttr[vo_id].stRect.s32Y = 0;
        board_config_.stOverlay0ChnAttr[vo_id].stRect.u32Height = board_config_.stOverLayer0DevSize.u32Height;
        board_config_.stOverlay0ChnAttr[vo_id].stRect.u32Width = board_config_.stOverLayer0DevSize.u32Width;

        BOARD_LOG_INFO("BoardContext EnableOverlay 3");
        AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_SetChnAttr(board_config_.VoOverLayer0[vo_id], 0, &board_config_.stOverlay0ChnAttr[vo_id]), "AR_MPI_VO_SetChnAttr");
        BOARD_LOG_INFO("BoardContext EnableOverlay 4");
        AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_EnableChn(board_config_.VoOverLayer0[vo_id], 0), "AR_MPI_VO_EnableChn");
        BOARD_LOG_INFO("VODP{} overlay0 enabled.", vo_id);

        return true;
    }

    static std::atomic<bool> s_overlay1_enabled[2]{false, false};
    bool BoardContext::EnableOverlay1(uint32_t vo_id, uint32_t start_x, uint32_t start_y, uint32_t width, uint32_t height)
    {
        if (s_overlay1_enabled[vo_id].load())
            return true;
        s_overlay1_enabled[vo_id].store(true);
        BOARD_LOG_INFO("BoardContext EnableOverlay1{} {}x{} at ({},{})!", vo_id, width, height, start_x, start_y);
        board_config_.stOverLayer1DevSize.u32Width = width;
        board_config_.stOverLayer1DevSize.u32Height = height;
        board_config_.stOverLayer1Attr[vo_id].bClusterMode = AR_FALSE;
        board_config_.stOverLayer1Attr[vo_id].bDoubleFrame = AR_FALSE;
        board_config_.stOverLayer1Attr[vo_id].enDstDynamicRange = DYNAMIC_RANGE_SDR8;
        board_config_.stOverLayer1Attr[vo_id].stDispRect.s32X = 0;
        board_config_.stOverLayer1Attr[vo_id].stDispRect.s32Y = 0;
        board_config_.stOverLayer1Attr[vo_id].stDispRect.u32Height = board_config_.stOverLayer1DevSize.u32Height;
        board_config_.stOverLayer1Attr[vo_id].stDispRect.u32Width = board_config_.stOverLayer1DevSize.u32Width;
        board_config_.stOverLayer1Attr[vo_id].stImageSize.u32Height = board_config_.stOverLayer1DevSize.u32Height;
        board_config_.stOverLayer1Attr[vo_id].stImageSize.u32Width = board_config_.stOverLayer1DevSize.u32Width;
        board_config_.stOverLayer1Attr[vo_id].enPixFormat = PIXEL_FORMAT_ARGB_4444;
        board_config_.stOverLayer1Attr[vo_id].u32DispFrmRt = 30;
        board_config_.stOverLayer1Attr[vo_id].u32Stride[0] = AR_ALIGN128(width * 2);
        board_config_.stOverLayer1Attr[vo_id].u32Stride[1] = 0;
        board_config_.stOverLayer1Attr[vo_id].u32Stride[2] = 0;
        board_config_.stOverLayer1Attr[vo_id].u32Stride[3] = 0;

        board_config_.stOverLayer1Attr[vo_id].memMode = VO_MEMORY_MODE_LOW;

        BOARD_LOG_INFO("BoardContext EnableOverlay1 0");
        AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_SetVideoLayerAttr(board_config_.VoOverLayer1[vo_id], &board_config_.stOverLayer1Attr[vo_id]), "AR_MPI_VO_SetVideoLayerAttr");

        POINT_S stOverlayPos = {0};
        stOverlayPos.s32X = start_x;
        stOverlayPos.s32Y = start_y;
        BOARD_LOG_INFO("BoardContext EnableOverlay1 1");
        AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_SetVideoLayerPos(board_config_.VoOverLayer1[vo_id], &stOverlayPos), "AR_MPI_VO_SetVideoLayerPos");

        /* ENABLE VO OVERLAYER1 */
        BOARD_LOG_INFO("BoardContext EnableOverlay1 2");
        AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_EnableVideoLayer(board_config_.VoOverLayer1[vo_id]), "AR_MPI_VO_EnableVideoLayer");

        /* SET AND ENABLE VO CHN */
        memset(&board_config_.stOverlay0ChnAttr[vo_id], 0, sizeof(VO_CHN_ATTR_S));
        board_config_.stOverlay0ChnAttr[vo_id].bDeflicker = AR_FALSE;
        board_config_.stOverlay0ChnAttr[vo_id].u32Priority = 0;
        board_config_.stOverlay0ChnAttr[vo_id].stRect.s32X = 0;
        board_config_.stOverlay0ChnAttr[vo_id].stRect.s32Y = 0;
        board_config_.stOverlay0ChnAttr[vo_id].stRect.u32Height = board_config_.stOverLayer1DevSize.u32Height;
        board_config_.stOverlay0ChnAttr[vo_id].stRect.u32Width = board_config_.stOverLayer1DevSize.u32Width;

        BOARD_LOG_INFO("BoardContext EnableOverlay 3");
        AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_SetChnAttr(board_config_.VoOverLayer1[vo_id], 0, &board_config_.stOverlay0ChnAttr[vo_id]), "AR_MPI_VO_SetChnAttr");
        BOARD_LOG_INFO("BoardContext EnableOverlay 4");
        AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_EnableChn(board_config_.VoOverLayer1[vo_id], 0), "AR_MPI_VO_EnableChn");
        BOARD_LOG_INFO("VODP{} overlay0 enabled.", vo_id);

        return true;
    }

    AR_U32 s_u32ColorTable[MAX_VO_COLOR_TABLE_SIZE] = {0};
    bool BoardContext::EnableOverlay1Indexed(uint32_t vo_id, uint32_t start_x, uint32_t start_y, uint32_t width, uint32_t height)
    {
        if (s_overlay1_enabled[vo_id].load())
            return true;
        s_overlay1_enabled[vo_id].store(true);
        BOARD_LOG_INFO("BoardContext EnableOverlay1Indexed{} {}x{} at ({},{})!", vo_id, width, height, start_x, start_y);
        board_config_.stOverLayer1DevSize.u32Width = width;
        board_config_.stOverLayer1DevSize.u32Height = height;
        board_config_.stOverLayer1Attr[vo_id].bClusterMode = AR_FALSE;
        board_config_.stOverLayer1Attr[vo_id].bDoubleFrame = AR_FALSE;
        board_config_.stOverLayer1Attr[vo_id].enDstDynamicRange = DYNAMIC_RANGE_SDR8;
        board_config_.stOverLayer1Attr[vo_id].stDispRect.s32X = 0;
        board_config_.stOverLayer1Attr[vo_id].stDispRect.s32Y = 0;
        board_config_.stOverLayer1Attr[vo_id].stDispRect.u32Height = board_config_.stOverLayer1DevSize.u32Height;
        board_config_.stOverLayer1Attr[vo_id].stDispRect.u32Width = board_config_.stOverLayer1DevSize.u32Width;
        board_config_.stOverLayer1Attr[vo_id].stImageSize.u32Height = board_config_.stOverLayer1DevSize.u32Height;
        board_config_.stOverLayer1Attr[vo_id].stImageSize.u32Width = board_config_.stOverLayer1DevSize.u32Width;
        board_config_.stOverLayer1Attr[vo_id].enPixFormat = INDEXED_OSD_FORMAT;

        board_config_.stOverLayer1Attr[vo_id].u32DispFrmRt = 30;
        board_config_.stOverLayer1Attr[vo_id].u32Stride[0] = AR_ALIGN128(width / local::OSD_UNITS_PER_BYTE);
        board_config_.stOverLayer1Attr[vo_id].u32Stride[1] = 0;
        board_config_.stOverLayer1Attr[vo_id].u32Stride[2] = 0;
        board_config_.stOverLayer1Attr[vo_id].u32Stride[3] = 0;

        board_config_.stOverLayer1Attr[vo_id].memMode = VO_MEMORY_MODE_LOW;

        BOARD_LOG_INFO("BoardContext EnableOverlay1Indexed 0");
        AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_SetVideoLayerAttr(board_config_.VoOverLayer1[vo_id], &board_config_.stOverLayer1Attr[vo_id]), "AR_MPI_VO_SetVideoLayerAttr");

        for (int i = 0; i < MAX_VO_COLOR_TABLE_SIZE; i++)
            s_u32ColorTable[i] = 0xff000000; // black(argb8888)
        s_u32ColorTable[0] = 0x0;            // full transparency(argb8888)

        uint32_t color_table_size = 2;
        switch (local::OSD_BITS_PER_PIXEL)
        {
        case 1:
            color_table_size = 2;
            break;
        case 2:
            color_table_size = 4;
            break;
        case 4:
            color_table_size = 16;
            break;
        case 8:
            color_table_size = 256;
            break;
        default:
            BOARD_LOG_ERROR("unsupported osd bits per pixel: {}", local::OSD_BITS_PER_PIXEL);
            return false;
        }
        AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_SetVideoLayerColorTable(board_config_.VoOverLayer1[vo_id], s_u32ColorTable, color_table_size), "AR_MPI_VO_SetVideoLayerColorTable");

        POINT_S stOverlay1Pos = {0};
        stOverlay1Pos.s32X = start_x;
        stOverlay1Pos.s32Y = start_y;
        BOARD_LOG_INFO("BoardContext EnableOverlay1Indexed 1");
        AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_SetVideoLayerPos(board_config_.VoOverLayer1[vo_id], &stOverlay1Pos), "AR_MPI_VO_SetVideoLayerPos");

        /* ENABLE VO OVERLAYER1 */
        BOARD_LOG_INFO("BoardContext EnableOverlay1Indexed 2");
        AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_EnableVideoLayer(board_config_.VoOverLayer1[vo_id]), "AR_MPI_VO_EnableVideoLayer");

        /* SET AND ENABLE VO CHN */
        memset(&board_config_.stOverlay1ChnAttr[vo_id], 0, sizeof(VO_CHN_ATTR_S));
        board_config_.stOverlay1ChnAttr[vo_id].bDeflicker = AR_FALSE;
        board_config_.stOverlay1ChnAttr[vo_id].u32Priority = 0;
        board_config_.stOverlay1ChnAttr[vo_id].stRect.s32X = 0;
        board_config_.stOverlay1ChnAttr[vo_id].stRect.s32Y = 0;
        board_config_.stOverlay1ChnAttr[vo_id].stRect.u32Height = board_config_.stOverLayer1DevSize.u32Height;
        board_config_.stOverlay1ChnAttr[vo_id].stRect.u32Width = board_config_.stOverLayer1DevSize.u32Width;

        BOARD_LOG_INFO("BoardContext EnableOverlay1Indexed 3");
        AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_SetChnAttr(board_config_.VoOverLayer1[vo_id], 0, &board_config_.stOverlay1ChnAttr[vo_id]), "AR_MPI_VO_SetChnAttr");
        BOARD_LOG_INFO("BoardContext EnableOverlay1Indexed 4");
        AR_API_ABORT_ON_ERROR(pfn_AR_MPI_VO_EnableChn(board_config_.VoOverLayer1[vo_id], 0), "AR_MPI_VO_EnableChn");
        BOARD_LOG_INFO("VODP{} overlay1Indexed enabled.", vo_id);

        return true;
    }

    bool BoardContext::DisableOverlay0(uint32_t vo_id)
    {
        if (!s_overlay0_enabled[vo_id].load())
            return true;
        s_overlay0_enabled[vo_id].store(false);
        BOARD_LOG_INFO("BoardContext DisableOverlay0 {}!", vo_id);
        AR_S32 ret;
        ret = pfn_AR_MPI_VO_DisableChn(board_config_.VoOverLayer0[vo_id], 0);
        if (AR_SUCCESS != ret)
        {
            BOARD_LOG_ERROR("AR_MPI_VO_DisableChn for:{} failed. s32Ret: {:x}", board_config_.VoOverLayer0[vo_id], ret);
            return false;
        }
        ret = pfn_AR_MPI_VO_DisableVideoLayer(board_config_.VoOverLayer0[vo_id]);
        if (AR_SUCCESS != ret)
        {
            BOARD_LOG_ERROR("AR_MPI_VO_DisableVideoLayer for:{} failed. s32Ret: {:x}", board_config_.VoOverLayer0[vo_id], ret);
            return false;
        }
        BOARD_LOG_INFO("VODP{} overlay0 disabled.", vo_id);
        return true;
    }

    bool BoardContext::DisableOverlay1(uint32_t vo_id)
    {
        if (!s_overlay1_enabled[vo_id].load())
            return true;
        s_overlay1_enabled[vo_id].store(false);
        BOARD_LOG_INFO("BoardContext DisableOverlay1 {}!", vo_id);
        AR_S32 ret;
        ret = pfn_AR_MPI_VO_DisableChn(board_config_.VoOverLayer1[vo_id], 0);
        if (AR_SUCCESS != ret)
        {
            BOARD_LOG_ERROR("AR_MPI_VO_DisableChn for:{} failed. s32Ret: {:x}", board_config_.VoOverLayer1[vo_id], ret);
            return false;
        }
        ret = pfn_AR_MPI_VO_DisableVideoLayer(board_config_.VoOverLayer1[vo_id]);
        if (AR_SUCCESS != ret)
        {
            BOARD_LOG_ERROR("AR_MPI_VO_DisableVideoLayer for:{} failed. s32Ret: {:x}", board_config_.VoOverLayer1[vo_id], ret);
            return false;
        }
        BOARD_LOG_INFO("VODP{} overlay1 disabled.", vo_id);
        return true;
    }

    bool BoardContext::SendFrameToOverlay(AR_S32 overlay_id, void *overlay_frame_info)
    {
        VIDEO_FRAME_INFO_S *frame_info = (VIDEO_FRAME_INFO_S *)overlay_frame_info;
        BOARD_LOG_INFO("BoardContext SendFrameToOverlay! overlay_id: {} width: {} height: {} format: {} frame_info_handle: {}",
                       overlay_id, frame_info->stVFrame.u32Width, frame_info->stVFrame.u32Height, frame_info->stVFrame.enPixelFormat, overlay_frame_info);
        if (AR_SUCCESS != pfn_AR_MPI_VO_SendFrame(overlay_id, 0, (VIDEO_FRAME_INFO_S *)overlay_frame_info, 0))
            return false;
        return true;
    }
}
