#include <util/util.h>
#include <enums.h>
#include <log.h>

#include <framework/util/json.h>

#include <fstream>
#include <vector>

#define AR_ALIGN128(_x) (((_x) + 0x7f) & ~0x7f)

using namespace local;

void CallCounter::Update()
{
    if (last_tick_ == 0)
    {
        last_tick_ = FMonotonicGetNs();
        return;
    }
    uint64_t curr_tick = FMonotonicGetNs();
    uint64_t new_tick = curr_tick - last_tick_;
    last_tick_ = curr_tick;
    tick_sum_ += new_tick;                /* add new value */
    tick_sum_ -= tick_list_[tick_index_]; /* subtract value falling off */
    tick_list_[tick_index_] = new_tick;   /* save new value so it can be subtracted later */
    if (++tick_index_ == samples_)
        tick_index_ = 0; /* inc buffer index */
    if (PRINT_INTERVAL_SEC > 0)
    {
        if (last_print_timestamp_ == 0)
        {
            last_print_timestamp_ = curr_tick;
            return;
        }
        if ((curr_tick - last_print_timestamp_) / OS_NS_PER_SEC > PRINT_INTERVAL_SEC)
        {
            double avg_calls_per_second = (double)OS_NS_PER_SEC * samples_ / tick_sum_;
            last_print_timestamp_ = curr_tick;
            BOARD_LOG_INFO("{} fps: {:.2f}", name_, avg_calls_per_second);
        }
    }
}

void GenTestVOMask(char *mask_data[2])
{
    for (int i = 0; i < 2; ++i) // 0 corresponds to left, 1 corresponds to right
    {
        mask_data[i] = new char[1920 * 1080 * 2];
        BOARD_LOG_INFO("GenTestVOMask {}:{}", i, (void *)mask_data[i]);
        memset(mask_data[i], 0, 1920 * 1080 * 2);
        for (int col = 0; col < 1920; ++col)
        {
            for (int row = 0; row < 1080; ++row)
            {
                int idx = (row * 1920 + col) * 2;
                int x_diff = col - 960;
                int y_diff = row - 540;
                if (x_diff * x_diff + y_diff * y_diff > 540 * 540)
                    mask_data[i][idx + 1] = 0xF0;
            }
        }
    }
}

void GenVOPupilMask(char *mask_data[2], int height)
{
    int stride = AR_ALIGN128(MAX_PUPIL_MASK_WIDTH / OSD_UNITS_PER_BYTE);
    for (int i = 0; i < 2; ++i) // 0 corresponds to left, 1 corresponds to right
    {
        mask_data[i] = new char[stride * height];
        BOARD_LOG_INFO("GenVOPupilMask {}:{}", i, (void *)mask_data[i]);
        memset(mask_data[i], 0, stride * height);
    }
}

void UpdateVOPupilMask(char *mask_data, int width, int height, bool cover_left)
{
    BOARD_LOG_INFO("UpdateVOPupilMask width:{} height:{}", width, height);
    int stride = AR_ALIGN128(MAX_PUPIL_MASK_WIDTH * 2); // ARGB_4444
    // memset(mask_data, 0, stride * height);
    for (int row = 0; row < height; ++row)
    {
        for (int col = 0; col < MAX_PUPIL_MASK_WIDTH; ++col)
        {
            if ((cover_left && (col <= width)) || ((!cover_left) && ((MAX_PUPIL_MASK_WIDTH - col) <= width)))
            { // all black
                mask_data[row * stride + col * 2] = 0;
                mask_data[row * stride + col * 2 + 1] = 0xF0;
            }
            else
            {
                mask_data[row * stride + col * 2] = 0;
                mask_data[row * stride + col * 2 + 1] = 0;
            }
        }
    }
}

void UpdateVOPupilMaskIndexed(char *mask_data, int width, int height, bool cover_left)
{
    BOARD_LOG_INFO("UpdateVOPupilMaskIndexed width:{} height:{}", width, height);
    int stride = AR_ALIGN128(MAX_PUPIL_MASK_WIDTH / OSD_UNITS_PER_BYTE);
    char tmp = 0;
    int char_idx = 0;
    for (int row = 0; row < height; ++row)
    {
        for (int col = 0; col < MAX_PUPIL_MASK_WIDTH; ++col)
        {
            int idx = row * MAX_PUPIL_MASK_WIDTH + col;
            if ((idx / OSD_UNITS_PER_BYTE) != char_idx)
            {
                int data_index = char_idx / (MAX_PUPIL_MASK_WIDTH / OSD_UNITS_PER_BYTE) * stride + char_idx % (MAX_PUPIL_MASK_WIDTH / OSD_UNITS_PER_BYTE);
                mask_data[data_index] = tmp;
                tmp = 0;
                char_idx = idx / OSD_UNITS_PER_BYTE;
            }
            if ((cover_left && (col <= width)) || ((!cover_left) && ((MAX_PUPIL_MASK_WIDTH - col) <= width)))
                tmp |= (1 << (OSD_BITS_PER_PIXEL * (OSD_UNITS_PER_BYTE - 1) - (idx % OSD_UNITS_PER_BYTE)));
        }
    }
    mask_data[stride * height - 1] = tmp;
}

void GenTestVOMaskBits(char *mask_data[2], int width, int height)
{
    int OSD_UNITS_PER_BYTE = 8 / OSD_BITS_PER_PIXEL;
    int stride = AR_ALIGN128(width / OSD_UNITS_PER_BYTE);
    for (int i = 0; i < 2; ++i) // 0 corresponds to left, 1 corresponds to right
    {
        mask_data[i] = new char[stride * height];
        BOARD_LOG_INFO("GenTestVOMask {}:{}", i, (void *)mask_data[i]);
        memset(mask_data[i], 0, stride * height);
        char tmp = 0;
        int char_idx = 0;
        for (int row = 0; row < height; ++row)
        {
            for (int col = 0; col < width; ++col)
            {
                int idx = row * width + col;
                if ((idx / OSD_BITS_PER_PIXEL) != char_idx)
                {
                    int data_index = char_idx / (width / OSD_BITS_PER_PIXEL) * stride + char_idx % (width / OSD_BITS_PER_PIXEL);
                    mask_data[i][data_index] = tmp;
                    tmp = 0;
                    char_idx = idx / OSD_BITS_PER_PIXEL;
                }
                int x_diff = col - (width / 2);
                int y_diff = row - (height / 2);
                if (x_diff * x_diff + y_diff * y_diff > height * height / 4)
                    tmp |= (1 << (OSD_BITS_PER_PIXEL * (OSD_UNITS_PER_BYTE - 1) - (idx % OSD_UNITS_PER_BYTE)));
            }
        }
        mask_data[i][stride * height - 1] = tmp;
    }
}

void GenVOMaskFromFile(char *mask_data[2])
{
    std::ifstream file("/usrdata/left_mask.bin", std::ios::binary);
    if (!file)
    {
        BOARD_LOG_ERROR("Failed to open left_mask.bin for reading!");
        return;
    }

    // 获取文件大小
    file.seekg(0, std::ios::end);
    size_t fileSize = file.tellg();
    file.seekg(0, std::ios::beg);

    // 分配缓冲区
    std::vector<char> buffer(fileSize);

    // 读取整个文件
    file.read(buffer.data(), fileSize);
    BOARD_LOG_INFO("GenVOMaskFromFile");
    // left
    memset(mask_data[0], 0, 1920 * 1080 * 2);
    for (int col = 0; col < 1920; ++col)
    {
        for (int row = 0; row < 1080; ++row)
        {
            int idx = row * 1920 + col;
            // mask_data[0][idx * 2 + 1] = (buffer[idx] << 4);
            if (buffer[idx] != 0)
                mask_data[0][idx * 2 + 1] = 0xF0;
        }
    }
    file.close();
    // right
    file = std::ifstream("/usrdata/right_mask.bin", std::ios::binary);
    if (!file)
    {
        BOARD_LOG_ERROR("Failed to open right_mask.bin for reading!");
        return;
    }
    file.read(buffer.data(), fileSize);
    memset(mask_data[1], 0, 1920 * 1080 * 2);
    for (int col = 0; col < 1920; ++col)
    {
        for (int row = 0; row < 1080; ++row)
        {
            int idx = row * 1920 + col;
            // mask_data[1][idx * 2 + 1] = (buffer[idx] << 4);
            if (buffer[idx] != 0)
                mask_data[1][idx * 2 + 1] = 0xF0;
        }
    }
    file.close();
}