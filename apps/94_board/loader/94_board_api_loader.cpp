#include <dlfcn.h>
#include <stdio.h>

#include <94_board.h>
#include <log.h>

PFN_AR_MPI_GDC_ADV_Query pfn_AR_MPI_GDC_ADV_Query{NULL};
PFN_AR_MPI_GDC_ADV_Process pfn_AR_MPI_GDC_ADV_Process{NULL};
PFN_AR_MPI_GDC_ADV_Config pfn_AR_MPI_GDC_ADV_Config{NULL};
PFN_AR_MPI_GDC_ADV_Start pfn_AR_MPI_GDC_ADV_Start{NULL};
PFN_AR_MPI_GDC_ADV_Stop pfn_AR_MPI_GDC_ADV_Stop{NULL};
PFN_AR_MPI_GDC_ADV_Config_CPUBp_Line pfn_AR_MPI_GDC_ADV_Config_CPUBp_Line{NULL};
PFN_AR_MPI_GDC_ADV_Get_FrmPts pfn_AR_MPI_GDC_ADV_Get_FrmPts{NULL};

PFN_AR_MPI_VIN_OpenDev pfn_AR_MPI_VIN_OpenDev{NULL};
PFN_AR_MPI_VIN_CloseDev pfn_AR_MPI_VIN_CloseDev{NULL};
PFN_AR_MPI_VIN_GetSensorObj pfn_AR_MPI_VIN_GetSensorObj{NULL};
PFN_AR_MPI_VIN_CloseSensorObj pfn_AR_MPI_VIN_CloseSensorObj{NULL};
PFN_AR_MPI_VIN_PipeBindSensor pfn_AR_MPI_VIN_PipeBindSensor = {NULL};

PFN_AR_MPI_SYS_Init pfn_AR_MPI_SYS_Init{NULL};
PFN_AR_MPI_SYS_Exit pfn_AR_MPI_SYS_Exit{NULL};
PFN_AR_MPI_SYS_Mmap pfn_AR_MPI_SYS_Mmap{NULL};

PFN_AR_MPI_VB_Init pfn_AR_MPI_VB_Init{NULL};
PFN_AR_MPI_VB_Exit pfn_AR_MPI_VB_Exit{NULL};
PFN_AR_MPI_VB_SetConfig pfn_AR_MPI_VB_SetConfig{NULL};
PFN_AR_MPI_VB_ExitModCommPool pfn_AR_MPI_VB_ExitModCommPool{NULL};
PFN_AR_MPI_VB_CreatePool pfn_AR_MPI_VB_CreatePool{NULL};
PFN_AR_MPI_VB_GetBlock pfn_AR_MPI_VB_GetBlock{NULL};
PFN_AR_MPI_VB_ReleaseBlock pfn_AR_MPI_VB_ReleaseBlock{NULL};
PFN_AR_MPI_VB_Handle2PoolId pfn_AR_MPI_VB_Handle2PoolId{NULL};
PFN_AR_MPI_VB_PhysAddr2Handle pfn_AR_MPI_VB_PhysAddr2Handle{NULL};
PFN_AR_MPI_VB_Handle2PhysAddr pfn_AR_MPI_VB_Handle2PhysAddr{NULL};
PFN_AR_MPI_VB_MmapPool pfn_AR_MPI_VB_MmapPool{NULL};
PFN_AR_MPI_VB_MunmapPool pfn_AR_MPI_VB_MunmapPool{NULL};
PFN_AR_MPI_VB_GetBlockVirAddr pfn_AR_MPI_VB_GetBlockVirAddr{NULL};

PFN_AR_MPI_VI_SetChnAttr pfn_AR_MPI_VI_SetChnAttr{NULL};
PFN_AR_MPI_VI_SetChnExtAttr pfn_AR_MPI_VI_SetChnExtAttr{NULL};
PFN_AR_MPI_VI_SetChnCmpAttr pfn_AR_MPI_VI_SetChnCmpAttr{NULL};
PFN_AR_MPI_VI_SetMipiBindDev pfn_AR_MPI_VI_SetMipiBindDev{NULL};
PFN_AR_MPI_VI_SetComboDevAttr pfn_AR_MPI_VI_SetComboDevAttr{NULL};
PFN_AR_MPI_VI_SetDevAttr pfn_AR_MPI_VI_SetDevAttr{NULL};
PFN_AR_MPI_VI_SetDevBindPipe pfn_AR_MPI_VI_SetDevBindPipe{NULL};
PFN_AR_MPI_VI_EnableDev pfn_AR_MPI_VI_EnableDev{NULL};
PFN_AR_MPI_VI_DisableDev pfn_AR_MPI_VI_DisableDev{NULL};
PFN_AR_MPI_VI_EnableChn pfn_AR_MPI_VI_EnableChn{NULL};
PFN_AR_MPI_VI_DisableChn pfn_AR_MPI_VI_DisableChn{NULL};
PFN_AR_MPI_VI_GetPipeExtAttr pfn_AR_MPI_VI_GetPipeExtAttr{NULL};
PFN_AR_MPI_VI_SetPipeExtAttr pfn_AR_MPI_VI_SetPipeExtAttr{NULL};
PFN_AR_MPI_VI_StartPipe pfn_AR_MPI_VI_StartPipe{NULL};
PFN_AR_MPI_VI_StopPipe pfn_AR_MPI_VI_StopPipe{NULL};
PFN_AR_MPI_VI_CreatePipe pfn_AR_MPI_VI_CreatePipe{NULL};
PFN_AR_MPI_VI_DestroyPipe pfn_AR_MPI_VI_DestroyPipe{NULL};
PFN_AR_MPI_VI_GetChnFrame pfn_AR_MPI_VI_GetChnFrame{NULL};
PFN_AR_MPI_VI_ReleaseChnFrame pfn_AR_MPI_VI_ReleaseChnFrame{NULL};
PFN_AR_MPI_VI_GetHightPricisionPipeFPS pfn_AR_MPI_VI_GetHightPricisionPipeFPS{NULL};
PFN_AR_MPI_VI_ReShapeChExt pfn_AR_MPI_VI_ReShapeChExt{NULL};

PFN_AR_MPI_VO_Enable pfn_AR_MPI_VO_Enable{NULL};
PFN_AR_MPI_VO_Disable pfn_AR_MPI_VO_Disable{NULL};
PFN_AR_MPI_VO_EnableVideoLayer pfn_AR_MPI_VO_EnableVideoLayer{NULL};
PFN_AR_MPI_VO_DisableVideoLayer pfn_AR_MPI_VO_DisableVideoLayer{NULL};
PFN_AR_MPI_VO_SetVideoLayerAttr pfn_AR_MPI_VO_SetVideoLayerAttr{NULL};
PFN_AR_MPI_VO_SetVideoLayerPos pfn_AR_MPI_VO_SetVideoLayerPos{NULL};
PFN_AR_MPI_VO_EnableChn pfn_AR_MPI_VO_EnableChn{NULL};
PFN_AR_MPI_VO_DisableChn pfn_AR_MPI_VO_DisableChn{NULL};
PFN_AR_MPI_VO_SetChnAttr pfn_AR_MPI_VO_SetChnAttr{NULL};
PFN_AR_MPI_VO_SetUserIntfSyncInfo pfn_AR_MPI_VO_SetUserIntfSyncInfo{NULL};
PFN_AR_MPI_VO_SetIrqAttr pfn_AR_MPI_VO_SetIrqAttr{NULL};
PFN_AR_MPI_VO_SubscribeEnable pfn_AR_MPI_VO_SubscribeEnable{NULL};
PFN_AR_MPI_VO_SubscribeDisable pfn_AR_MPI_VO_SubscribeDisable{NULL};
PFN_AR_MPI_VO_SetPubAttr pfn_AR_MPI_VO_SetPubAttr{NULL};
PFN_AR_MPI_VO_SetDevFrameRate pfn_AR_MPI_VO_SetDevFrameRate{NULL};
PFN_AR_MPI_VO_SetStartAttr pfn_AR_MPI_VO_SetStartAttr{NULL};
PFN_AR_MPI_VO_SetLowdelayAttr pfn_AR_MPI_VO_SetLowdelayAttr{NULL};
PFN_AR_MPI_VO_Dsi_SetAttr pfn_AR_MPI_VO_Dsi_SetAttr{NULL};
PFN_AR_MPI_VO_Dsi_Enable pfn_AR_MPI_VO_Dsi_Enable{NULL};
PFN_AR_MPI_VO_SendFrame pfn_AR_MPI_VO_SendFrame{NULL};
PFN_AR_MPI_VO_SetVideoLayerColorTable pfn_AR_MPI_VO_SetVideoLayerColorTable{NULL};

PFN_AR_MPI_VO_EnableLineBuffer pfn_AR_MPI_VO_EnableLineBuffer{NULL};
PFN_AR_MPI_VO_DisableLineBuffer pfn_AR_MPI_VO_DisableLineBuffer{NULL};
PFN_AR_MPI_VO_ResetLineBufferPrs pfn_AR_MPI_VO_ResetLineBufferPrs{NULL};

PFN_AR_MPI_ISP_Init pfn_AR_MPI_ISP_Init{NULL};
PFN_AR_MPI_ISP_Run pfn_AR_MPI_ISP_Run{NULL};
PFN_AR_MPI_ISP_MemInit pfn_AR_MPI_ISP_MemInit{NULL};
PFN_AR_MPI_ISP_SetPubAttr pfn_AR_MPI_ISP_SetPubAttr{NULL};
PFN_AR_MPI_ISP_Exit pfn_AR_MPI_ISP_Exit{NULL};

PFN_ar_hal_sys_mmz_alloc pfn_ar_hal_sys_mmz_alloc{NULL};
PFN_ar_hal_sys_mmz_alloc_cached pfn_ar_hal_sys_mmz_alloc_cached{NULL};
PFN_ar_hal_sys_mmz_flush_cache pfn_ar_hal_sys_mmz_flush_cache{NULL};
PFN_ar_hal_sys_mmz_free pfn_ar_hal_sys_mmz_free{NULL};
PFN_ar_hal_dp_rx_set_edid pfn_ar_hal_dp_rx_set_edid{NULL};
PFN_ar_hal_dp_rx_get_hpd_status pfn_ar_hal_dp_rx_get_hpd_status{NULL};
PFN_ar_hal_dp_rx_set_hpd_status pfn_ar_hal_dp_rx_set_hpd_status{NULL};

PFN_ar_log_func pfn_ar_log_func{NULL};
PFN_ar_log_func_raw pfn_ar_log_func_raw{NULL};
PFN_ar_log_init pfn_ar_log_init{NULL};
PFN_ar_memset pfn_ar_memset{NULL};

PFN_ar_queue_create pfn_ar_queue_create{NULL};
PFN_ar_queue_push_force pfn_ar_queue_push_force{NULL};
PFN_ar_queue_pop_timeout pfn_ar_queue_pop_timeout{NULL};

typedef void *LibraryHandle;

static LibraryHandle LibraryOpen(const char *path)
{
    // When loading the library, we use RTLD_LAZY so that not all symbols have to be
    // resolved at this time (which improves performance). Note that if not all symbols
    // can be resolved, this could cause crashes later.
    // For experimenting/debugging: Define the LD_BIND_NOW environment variable to force all
    // symbols to be resolved here.
    return dlopen(path, RTLD_LAZY | RTLD_GLOBAL);
}

static const char *LibraryOpenError(const char *path)
{
    (void)path;
    return dlerror();
}

static void LibraryClose(LibraryHandle library) { dlclose(library); }

static void *LibraryGetProcAddr(/*LibraryHandle library,*/ const char *name)
{
    // assert(library);
    return dlsym(NULL, name);
}

static const char *LibraryGetProcAddrError(const char *name)
{
    (void)name;
    return dlerror();
}
// if target library has been opened with RTLD_GLOBAL, we don't need its handle here
static void *LoadFunction(/*LibraryHandle dl_handle,*/ const char *function_name)
{
    void *func = LibraryGetProcAddr(function_name);

    const char *dl_error = LibraryGetProcAddrError(function_name);

    if (!func || dl_error != NULL)
    {
        BOARD_LOG_ERROR("ERROR! {}: {}", __FUNCTION__, function_name);
    }
    return func;
}

// if target library has been opened with RTLD_GLOBAL, we don't need its handle here
static void *LoadVar(/*LibraryHandle dl_handle,*/ const char *var_name)
{
    void *var = LibraryGetProcAddr(var_name);

    const char *dl_error = LibraryGetProcAddrError(var_name);

    if (!var || dl_error != NULL)
    {
        BOARD_LOG_ERROR("ERROR! {}: {}", __FUNCTION__, var_name);
    }
    return var;
}

static void LibraryCleanError()
{
    dlerror();
}

static LibraryHandle LoadLibrary(const char *path)
{
    LibraryCleanError();
    LibraryHandle dl_handle = LibraryOpen(path);

    const char *dl_error = LibraryOpenError(path);

    if (!dl_handle || dl_error != NULL)
    {
        BOARD_LOG_ERROR("Failed to open library:{}, error:{}", path, dl_error);
    }
    return dl_handle;
}

static const char *libraries[] = {
    "libosal.so",
    "libutils.so",
    "libhal_dbglog.so",
    "libhal_ge2d.so",
    "libhal_vb.so",
    "libhal_sys.so",
    "libhal_vdec.so",
    "libhal_venc.so",
    "libhal_vin.so",
    "libhal_vo.so",
    "libhal_aio.so",
    "libhal_region.so",
    "libhal_clk.so",
    "libhal_scaler.so",
    "libhal_gdc.so",
    "libhal_npu.so",
    "libhal_dsp.so",
    "libhal_vgs.so",
    "libbinder_ipc.so",
    "libhal_proc.so",
    "libhal_adec.so",
    "libhal_aenc.so",
    "libmpi_dbglog.so",
    "libmpi_vin.so",
    "libmpi_vo.so",
    "libmpi_vb.so",
    "libmpi_vdec.so",
    "libmpi_venc.so",
    "libmpi_vpss.so",
    "libmpi_sys.so",
    "libmpi_hdmi.so",
    "libmpi_region.so",
    "libmpi_scaler.so",
    "libmpi_gdc.so",
    "libmpi_ifc.so",
    "libmpi_ai.so",
    "libmpi_ao.so",
    "libmpi_vgs.so",
    "libmpi_adec.so",
    "libmpi_aenc.so",
    "libmpi_acodec.so",
    "libmpp_service.so",
    "libtools.so",
    "libhal_gpio.so",
    "libhal_i2c.so",
    "libhal_ive.so",
    "libhal_dp.so",
    "libsns_dp_rx.so"};

#define LOAD_FUNCTION(__FUNCTION_NAME__)                                                 \
    pfn_##__FUNCTION_NAME__ = (PFN_##__FUNCTION_NAME__)LoadFunction(#__FUNCTION_NAME__); \
    if (pfn_##__FUNCTION_NAME__)                                                         \
    {                                                                                    \
        BOARD_LOG_INFO("Load function {} success", #__FUNCTION_NAME__);                  \
    }                                                                                    \
    else                                                                                 \
    {                                                                                    \
        BOARD_LOG_ERROR("Load function {} falied", #__FUNCTION_NAME__);                  \
    }

bool LoadBoardSymbols()
{
    for (int i = 0; i < sizeof(libraries) / sizeof(char *); i++)
    {
        LibraryHandle dl_handle = LoadLibrary(libraries[i]);
        if (!dl_handle)
        {
            sleep(1);
            abort();
        }
        else
        {
            BOARD_LOG_INFO("{} load success", libraries[i]);
        }
    }

    LOAD_FUNCTION(AR_MPI_GDC_ADV_Query);
    LOAD_FUNCTION(AR_MPI_GDC_ADV_Process);
    LOAD_FUNCTION(AR_MPI_GDC_ADV_Config);
    LOAD_FUNCTION(AR_MPI_GDC_ADV_Start);
    LOAD_FUNCTION(AR_MPI_GDC_ADV_Stop);
    LOAD_FUNCTION(AR_MPI_GDC_ADV_Config_CPUBp_Line);
    LOAD_FUNCTION(AR_MPI_GDC_ADV_Get_FrmPts);

    LOAD_FUNCTION(AR_MPI_VIN_OpenDev);
    LOAD_FUNCTION(AR_MPI_VIN_CloseDev);
    LOAD_FUNCTION(AR_MPI_VIN_GetSensorObj);
    LOAD_FUNCTION(AR_MPI_VIN_CloseSensorObj);
    LOAD_FUNCTION(AR_MPI_VIN_PipeBindSensor);

    LOAD_FUNCTION(AR_MPI_SYS_Init);
    LOAD_FUNCTION(AR_MPI_SYS_Exit);
    LOAD_FUNCTION(AR_MPI_SYS_Mmap);

    LOAD_FUNCTION(AR_MPI_VB_Init);
    LOAD_FUNCTION(AR_MPI_VB_Exit);
    LOAD_FUNCTION(AR_MPI_VB_SetConfig);
    LOAD_FUNCTION(AR_MPI_VB_ExitModCommPool);
    LOAD_FUNCTION(AR_MPI_VB_CreatePool);
    LOAD_FUNCTION(AR_MPI_VB_GetBlock);
    LOAD_FUNCTION(AR_MPI_VB_ReleaseBlock);
    LOAD_FUNCTION(AR_MPI_VB_Handle2PoolId);
    LOAD_FUNCTION(AR_MPI_VB_Handle2PhysAddr);
    LOAD_FUNCTION(AR_MPI_VB_PhysAddr2Handle);
    LOAD_FUNCTION(AR_MPI_VB_MmapPool);
    LOAD_FUNCTION(AR_MPI_VB_MunmapPool);
    LOAD_FUNCTION(AR_MPI_VB_GetBlockVirAddr);

    LOAD_FUNCTION(AR_MPI_VI_SetChnAttr);
    LOAD_FUNCTION(AR_MPI_VI_SetChnExtAttr);
    LOAD_FUNCTION(AR_MPI_VI_SetChnCmpAttr);
    LOAD_FUNCTION(AR_MPI_VI_SetMipiBindDev);
    LOAD_FUNCTION(AR_MPI_VI_SetComboDevAttr);
    LOAD_FUNCTION(AR_MPI_VI_SetDevAttr);
    LOAD_FUNCTION(AR_MPI_VI_SetDevBindPipe);
    LOAD_FUNCTION(AR_MPI_VI_EnableDev);
    LOAD_FUNCTION(AR_MPI_VI_DisableDev);
    LOAD_FUNCTION(AR_MPI_VI_EnableChn);
    LOAD_FUNCTION(AR_MPI_VI_DisableChn);
    LOAD_FUNCTION(AR_MPI_VI_GetPipeExtAttr);
    LOAD_FUNCTION(AR_MPI_VI_SetPipeExtAttr);
    LOAD_FUNCTION(AR_MPI_VI_StartPipe);
    LOAD_FUNCTION(AR_MPI_VI_StopPipe);
    LOAD_FUNCTION(AR_MPI_VI_CreatePipe);
    LOAD_FUNCTION(AR_MPI_VI_DestroyPipe);
    LOAD_FUNCTION(AR_MPI_VI_GetChnFrame);
    LOAD_FUNCTION(AR_MPI_VI_ReleaseChnFrame);

    LOAD_FUNCTION(AR_MPI_VI_GetHightPricisionPipeFPS);
    LOAD_FUNCTION(AR_MPI_VI_ReShapeChExt);

    LOAD_FUNCTION(AR_MPI_VO_Disable);
    LOAD_FUNCTION(AR_MPI_VO_DisableVideoLayer);
    LOAD_FUNCTION(AR_MPI_VO_Enable);
    LOAD_FUNCTION(AR_MPI_VO_EnableVideoLayer);
    LOAD_FUNCTION(AR_MPI_VO_SetVideoLayerAttr);
    LOAD_FUNCTION(AR_MPI_VO_SetVideoLayerPos);
    LOAD_FUNCTION(AR_MPI_VO_EnableChn);
    LOAD_FUNCTION(AR_MPI_VO_DisableChn);
    LOAD_FUNCTION(AR_MPI_VO_SetChnAttr);
    LOAD_FUNCTION(AR_MPI_VO_SetUserIntfSyncInfo);
    LOAD_FUNCTION(AR_MPI_VO_SetIrqAttr);
    LOAD_FUNCTION(AR_MPI_VO_SubscribeEnable);
    LOAD_FUNCTION(AR_MPI_VO_SubscribeDisable);
    LOAD_FUNCTION(AR_MPI_VO_SetPubAttr);
    LOAD_FUNCTION(AR_MPI_VO_SetDevFrameRate);
    LOAD_FUNCTION(AR_MPI_VO_SetStartAttr);
    LOAD_FUNCTION(AR_MPI_VO_SetLowdelayAttr);
    LOAD_FUNCTION(AR_MPI_VO_Dsi_SetAttr);
    LOAD_FUNCTION(AR_MPI_VO_Dsi_Enable);
    LOAD_FUNCTION(AR_MPI_VO_SendFrame);
    LOAD_FUNCTION(AR_MPI_VO_SetVideoLayerColorTable);

    LOAD_FUNCTION(AR_MPI_VO_EnableLineBuffer);
    LOAD_FUNCTION(AR_MPI_VO_DisableLineBuffer);
    LOAD_FUNCTION(AR_MPI_VO_ResetLineBufferPrs);

    LOAD_FUNCTION(AR_MPI_ISP_Init);
    LOAD_FUNCTION(AR_MPI_ISP_Run);
    LOAD_FUNCTION(AR_MPI_ISP_MemInit);
    LOAD_FUNCTION(AR_MPI_ISP_SetPubAttr);
    LOAD_FUNCTION(AR_MPI_ISP_Exit);

    LOAD_FUNCTION(ar_hal_dp_rx_set_edid);
    LOAD_FUNCTION(ar_hal_dp_rx_get_hpd_status);
    LOAD_FUNCTION(ar_hal_dp_rx_set_hpd_status);
    LOAD_FUNCTION(ar_hal_sys_mmz_alloc);
    LOAD_FUNCTION(ar_hal_sys_mmz_alloc_cached);
    LOAD_FUNCTION(ar_hal_sys_mmz_flush_cache);
    LOAD_FUNCTION(ar_hal_sys_mmz_free);

    LOAD_FUNCTION(ar_log_func);
    LOAD_FUNCTION(ar_log_init);
    LOAD_FUNCTION(ar_log_func_raw);
    LOAD_FUNCTION(ar_memset);

    LOAD_FUNCTION(ar_queue_create);
    LOAD_FUNCTION(ar_queue_push_force);
    LOAD_FUNCTION(ar_queue_pop_timeout);

    return true;
}
