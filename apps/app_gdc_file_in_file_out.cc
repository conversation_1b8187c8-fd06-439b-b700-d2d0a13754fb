/**
 * @file app_gdc_file_in_file_out.cc
 * @brief This file contains main function to do unit test
 *
 * This program sets up a File-in--GDC--File-out pipeline
 * to verify GDC functionality
 *
 * <AUTHOR> <EMAIL>
 * @date 07/04/2025
 */

#define NRPLUGIN

#include <apps/stubs/util.h>

#include <heron/model/glasses_config.h>
#include <heron/dispatch/dispatcher_wrapper.h>
#include <heron/util/math_tools.h>
#include <heron/util/warp.h>
#include <heron/model/model_manager.h>

#include <framework/util/json.h>

#include <fstream>

#define STB_IMAGE_IMPLEMENTATION
#include "stb_image.h"
#define STB_IMAGE_WRITE_IMPLEMENTATION
#include "stb_image_write.h"

using namespace heron;
using namespace heron::dispatch;
using namespace heron::model;

struct TestCase
{
    std::string src_img_full_path;
    std::string glasses_config_full_path;
    std::string head_transforms_file_path;
    Transform quad_to_world;
    Vector2f quad_size_meters;
    Transform left_display_to_head;
    Transform right_display_to_head;
    bool do_tweak_mesh = false;
};
static TestCase s_test_case;

static bool PopulateTestCase(const std::string &test_case_string)
{
    Json::Value json_root;
    Json::CharReaderBuilder json_builder;
    json_builder["collectComments"] = false;
    JSONCPP_STRING json_errs;
    std::istringstream json_stream(test_case_string);
    if (!parseFromStream(json_builder, json_stream, &json_root, &json_errs))
    {
        HERON_LOG_ERROR("Parse test_case_string error, json_errs = {} test_case_string_size:{}", json_errs.c_str(), test_case_string.size());
        return false;
    }
    if (json_root.isMember("src_img_full_path"))
        s_test_case.src_img_full_path = json_root["src_img_full_path"].asString();
    else
    {
        HERON_LOG_ERROR("No src_img_full_path in config");
        return false;
    }
    if (json_root.isMember("glasses_config_full_path"))
        s_test_case.glasses_config_full_path = json_root["glasses_config_full_path"].asString();
    else
    {
        HERON_LOG_ERROR("No glasses_config_full_path in config");
        return false;
    }
    if (json_root.isMember("head_transforms_file_path"))
        s_test_case.head_transforms_file_path = json_root["head_transforms_file_path"].asString();
    else
    {
        HERON_LOG_ERROR("No head_transforms_file_path in config");
        return false;
    }
    if (json_root.isMember("quad"))
    {
        const Json::Value &quad = json_root["quad"];
        if (quad.isMember("position"))
        {
            const Json::Value &position = quad["position"];
            s_test_case.quad_to_world.position = Vector3f(position[0].asFloat(), position[1].asFloat(), position[2].asFloat());
        }
        else
        {
            HERON_LOG_ERROR("No quad position in config");
            return false;
        }
        if (quad.isMember("rotation"))
        {
            const Json::Value &rotation = quad["rotation"];
            s_test_case.quad_to_world.rotation = Quatf(rotation[3].asFloat(), rotation[0].asFloat(), rotation[1].asFloat(), rotation[2].asFloat());
        }
        else
        {
            HERON_LOG_ERROR("No quad rotation in config");
            return false;
        }
        if (quad.isMember("size_meters"))
        {
            const Json::Value &size_meters = quad["size_meters"];
            s_test_case.quad_size_meters = Vector2f(size_meters[0].asFloat(), size_meters[1].asFloat());
        }
        else
        {
            HERON_LOG_ERROR("No quad size_meters in config");
            return false;
        }
    }
    else
    {
        HERON_LOG_ERROR("No quad in config");
        return false;
    }
    if (json_root.isMember("left_display_to_head"))
    {
        const Json::Value &left_display_to_head = json_root["left_display_to_head"];
        if (left_display_to_head.isMember("position"))
        {
            const Json::Value &position = left_display_to_head["position"];
            s_test_case.left_display_to_head.position = Vector3f(position[0].asFloat(), position[1].asFloat(), position[2].asFloat());
        }
        else
        {
            HERON_LOG_ERROR("No left_display_to_head position in config");
            return false;
        }
        if (left_display_to_head.isMember("rotation"))
        {
            const Json::Value &rotation = left_display_to_head["rotation"];
            s_test_case.left_display_to_head.rotation = Quatf(rotation[3].asFloat(), rotation[0].asFloat(), rotation[1].asFloat(), rotation[2].asFloat());
        }
        else
        {
            HERON_LOG_ERROR("No left_display_to_head rotation in config");
            return false;
        }
    }
    else
    {
        HERON_LOG_ERROR("No left_display_to_head in config");
        return false;
    }
    if (json_root.isMember("right_display_to_head"))
    {
        const Json::Value &right_display_to_head = json_root["right_display_to_head"];
        if (right_display_to_head.isMember("position"))
        {
            const Json::Value &position = right_display_to_head["position"];
            s_test_case.right_display_to_head.position = Vector3f(position[0].asFloat(), position[1].asFloat(), position[2].asFloat());
        }
        else
        {
            HERON_LOG_ERROR("No right_display_to_head position in config");
            return false;
        }
        if (right_display_to_head.isMember("rotation"))
        {
            const Json::Value &rotation = right_display_to_head["rotation"];
            s_test_case.right_display_to_head.rotation = Quatf(rotation[3].asFloat(), rotation[0].asFloat(), rotation[1].asFloat(), rotation[2].asFloat());
        }
        else
        {
            HERON_LOG_ERROR("No right_display_to_head rotation in config");
            return false;
        }
    }
    else
    {
        HERON_LOG_ERROR("No right_display_to_head in config");
        return false;
    }
    if (json_root.isMember("do_tweak_mesh"))
        s_test_case.do_tweak_mesh = json_root["do_tweak_mesh"].asBool();
    return true;
}

static void DumpFrameBytes(const std::string &file_full_path, const DpFrameData &out_buffer)
{
    // Write data to the binary file
    int height = out_buffer.height;
    int width = out_buffer.width;
    std::ofstream outputFile(file_full_path, std::ios::binary);
    if (!outputFile)
    {
        HERON_LOG_ERROR("Error opening file {} for writing.", file_full_path);
        return;
    }
    for (int i = 0; i < height; i++)
    {
        outputFile.write((char *)out_buffer.data[0] + i * out_buffer.strides[0], width);
    }
    for (int i = 0; i < height; i++)
    {
        outputFile.write((char *)out_buffer.data[1] + i * out_buffer.strides[1], width);
    }
    for (int i = 0; i < height; i++)
    {
        outputFile.write((char *)out_buffer.data[2] + i * out_buffer.strides[2], width);
    }
    // Close the file
    // outputFile.close();
}

static void DumpFrameAsPNG(const std::string &file_full_path, const DpFrameData &out_buffer)
{
    int height = out_buffer.height;
    int width = out_buffer.width;
    //  1. 将Planar数据转换为Interleaved格式 (RRR...GGG...BBB... -> RGBRGBRGB...)
    std::vector<unsigned char> interleaved(width * height * 3);
    for (int y = 0; y < height; y++)
    {
        for (int x = 0; x < width; x++)
        {
            int planar_idx = y * out_buffer.strides[0] + x;
            int interleaved_idx = (y * width + x) * 3;

            interleaved[interleaved_idx + 0] = out_buffer.data[0][planar_idx]; // R
            interleaved[interleaved_idx + 1] = out_buffer.data[1][planar_idx]; // G
            interleaved[interleaved_idx + 2] = out_buffer.data[2][planar_idx]; // B
        }
    }

    // 2. 使用stb_image_write保存为PNG
    int success = stbi_write_png(
        file_full_path.c_str(),
        width,
        height,
        3, // 3 channels (RGB)
        interleaved.data(),
        width * 3 // 每行的字节数 = 宽度 * 3 (R+G+B)
    );

    if (!success)
    {
        HERON_LOG_ERROR("Error writing file {} for writing.", file_full_path);
    }
    else
    {
        HERON_LOG_INFO("Saved PNG: {}", file_full_path);
    }
}

static DpFrameData s_in_buffer, s_out_buffer;
static GdcInitConfig s_gdc_init_config[DISPLAY_USAGE_COUNT] = {};
static uint64_t s_warp_physical_addr[DISPLAY_USAGE_COUNT] = {};
static void *s_warp_virtual_addr[DISPLAY_USAGE_COUNT] = {};
static uint64_t s_mesh_physical_addr[DISPLAY_USAGE_COUNT] = {};
static void *s_mesh_virtual_addr[DISPLAY_USAGE_COUNT] = {};
static uint64_t s_weight_physical_addr[DISPLAY_USAGE_COUNT] = {};
static void *s_weight_virtual_addr[DISPLAY_USAGE_COUNT] = {};
static bool PopulateInOutFrame(const std::string &img_in_full_path)
{
    // Load image (force RGB even if original has alpha)
    int width, height, channels;
    unsigned char *data = stbi_load(img_in_full_path.c_str(), &width, &height, &channels, STBI_rgb);
    if (!data)
    {
        HERON_LOG_ERROR("Error loading image {}: {}", img_in_full_path, stbi_failure_reason());
        return false;
    }
    HERON_LOG_INFO("image loaded of size {}x{}x{}", width, height, channels);
    uint64_t physical_addr = {0};
    void *virtual_addr = {NULL};
    uint32_t channel_stride = ALIGN128(width);
    uint32_t channel_size = channel_stride * height;
    uint32_t img_data_size = channel_stride * height * 3;
    HERON_LOG_INFO("img mmz buffer size:{}", img_data_size);
    if (!DispatcherWrapper::GetInstance()->ARMmzAlloc(&physical_addr, &virtual_addr, "img_in", NULL, img_data_size))
    {
        HERON_LOG_ERROR("alloc mmz buffer for img in error");
    }

    s_in_buffer.width = width;
    s_in_buffer.height = height;
    s_in_buffer.strides[0] = channel_stride;
    s_in_buffer.strides[1] = channel_stride;
    s_in_buffer.strides[2] = channel_stride;
    s_in_buffer.data[0] = (char *)virtual_addr;
    s_in_buffer.data[1] = s_in_buffer.data[0] + channel_size;
    s_in_buffer.data[2] = s_in_buffer.data[1] + channel_size;
    s_in_buffer.data_ext[0] = (char *)physical_addr;
    s_in_buffer.data_ext[1] = s_in_buffer.data_ext[0] + channel_size;
    s_in_buffer.data_ext[2] = s_in_buffer.data_ext[1] + channel_size;

    // Convert interleaved RGBRGBRGB... to planar RRR...GGG...BBB...
    for (int y = 0; y < height; y++)
    {
        for (int x = 0; x < width; x++)
        {
            int idx = y * s_in_buffer.strides[0] + x;
            int pixel_idx = (y * width + x) * 3;            // 3 channels (R,G,B)
            s_in_buffer.data[0][idx] = data[pixel_idx + 0]; // Red
            s_in_buffer.data[1][idx] = data[pixel_idx + 1]; // Green
            s_in_buffer.data[2][idx] = data[pixel_idx + 2]; // Blue
        }
    }
    // Clean up
    stbi_image_free(data);

    if (!DispatcherWrapper::GetInstance()->ARMmzAlloc(&physical_addr, &virtual_addr, "img_out", NULL, img_data_size))
    {
        HERON_LOG_ERROR("alloc mmz buffer for img out error");
    }

    s_out_buffer.width = width;
    s_out_buffer.height = height;
    s_out_buffer.strides[0] = channel_stride;
    s_out_buffer.strides[1] = channel_stride;
    s_out_buffer.strides[2] = channel_stride;
    s_out_buffer.data[0] = (char *)virtual_addr;
    s_out_buffer.data[1] = s_out_buffer.data[0] + channel_size;
    s_out_buffer.data[2] = s_out_buffer.data[1] + channel_size;
    s_out_buffer.data_ext[0] = (char *)physical_addr;
    s_out_buffer.data_ext[1] = s_out_buffer.data_ext[0] + channel_size;
    s_out_buffer.data_ext[2] = s_out_buffer.data_ext[1] + channel_size;

    return true;
}

static bool AllocMmzBuffers(uint32_t src_width, uint32_t src_height)
{
    uint32_t mesh_cols = DIV_CEIL(src_width, ROW_COUNT_IN_BLOCK) + 1;
    uint32_t mesh_rows = DIV_CEIL(src_height, ROW_COUNT_IN_BLOCK) + 1;
    uint32_t warp_data_size = mesh_cols * mesh_rows * 3 * 3 * sizeof(float);
    for (int display = 0; display < DISPLAY_USAGE_COUNT; ++display)
    {
        HERON_LOG_INFO("warp{}:{}x{} mmz buffer size:{}", display, mesh_cols, mesh_rows, warp_data_size);
        if (!DispatcherWrapper::GetInstance()->ARMmzAlloc(&s_warp_physical_addr[display], &s_warp_virtual_addr[display], "warp", NULL, warp_data_size))
        {
            HERON_LOG_ERROR("alloc mmz buffer for warp{} error", display);
            return false;
        }
        s_gdc_init_config[display].metadata.warp_data_data = (const char *)s_warp_physical_addr[display];
        s_gdc_init_config[display].metadata.warp_data_size = warp_data_size;

        uint32_t mesh_data_size = mesh_cols * mesh_rows * 2 * sizeof(float);
        HERON_LOG_INFO("mesh{}:{}x{} mmz buffer size:{}", display, mesh_cols, mesh_rows, mesh_data_size);
        if (!DispatcherWrapper::GetInstance()->ARMmzAlloc(&s_mesh_physical_addr[display], &s_mesh_virtual_addr[display], "mesh", NULL, mesh_data_size))
        {
            HERON_LOG_ERROR("alloc mmz buffer for mesh{} error", display);
            return false;
        }
        s_gdc_init_config[display].metadata.mesh_data_data = (const char *)s_mesh_physical_addr[display];
        s_gdc_init_config[display].metadata.mesh_data_size = mesh_data_size;

        uint32_t weight_data_size = ROW_COUNT_IN_BLOCK * COLUMN_COUNT_IN_BLOCK * 4 * sizeof(float);
        HERON_LOG_INFO("weight{} mmz buffer size:{}", display, weight_data_size);
        if (!DispatcherWrapper::GetInstance()->ARMmzAlloc(&s_weight_physical_addr[display], &s_weight_virtual_addr[display], "weight", NULL, weight_data_size))
        {
            HERON_LOG_ERROR("alloc mmz buffer for weight{} error", display);
            return false;
        }
        s_gdc_init_config[display].metadata.weight_data_data = (const char *)s_weight_physical_addr[display];
        s_gdc_init_config[display].metadata.weight_data_size = weight_data_size;
    }
    return true;
}

static bool ReleaseMmzBuffers()
{
    HERON_LOG_INFO("Releasing mmz buffers");
    if (s_in_buffer.data[0])
        DispatcherWrapper::GetInstance()->ARMmzDealloc((uint64_t)s_in_buffer.data_ext[0], (void *)s_in_buffer.data[0]);
    if (s_out_buffer.data[0])
        DispatcherWrapper::GetInstance()->ARMmzDealloc((uint64_t)s_out_buffer.data_ext[0], (void *)s_out_buffer.data[0]);
    for (int display = 0; display < DISPLAY_USAGE_COUNT; ++display)
    {
        if (s_warp_virtual_addr[display])
            DispatcherWrapper::GetInstance()->ARMmzDealloc(s_warp_physical_addr[display], s_warp_virtual_addr[display]);
        if (s_mesh_virtual_addr[display])
            DispatcherWrapper::GetInstance()->ARMmzDealloc(s_mesh_physical_addr[display], s_mesh_virtual_addr[display]);
        if (s_weight_virtual_addr[display])
            DispatcherWrapper::GetInstance()->ARMmzDealloc(s_weight_physical_addr[display], s_weight_virtual_addr[display]);
    }
    HERON_LOG_INFO("mmz buffers released");
    return true;
}

static GDCQuadWarpPtr s_gdc_warp[DISPLAY_USAGE_COUNT];
static void GenGDCWarpMatrix(float *matrix, const Transform &to_transform)
{
    Mat3f warp_matrix_gdc33;

    s_gdc_warp[DISPLAY_USAGE_LEFT]->SetToTransform(to_transform);
    s_gdc_warp[DISPLAY_USAGE_LEFT]->DoWarp();
    s_gdc_warp[DISPLAY_USAGE_LEFT]->GetGDCWarpMatrix33(warp_matrix_gdc33);

    for (int i = 0; i < 9; i++) // seems has better performance than Eigen::Map<Eigen::Matrix<float, 3, 3, Eigen::RowMajor>>
        matrix[i] = warp_matrix_gdc33(i / 3, i % 3);
}

static void InitWarppers()
{
    HERON_LOG_INFO("Initing warppers");
    Vector2i dp_src_size_pixel(s_in_buffer.width, s_in_buffer.height);
    Vector2f canvas_base_size_meters(s_test_case.quad_size_meters.x(), s_test_case.quad_size_meters.y());
    for (uint32_t display = 0; display < DISPLAY_USAGE_COUNT; ++display)
    {
        float fc[2]{ModelManager::GetInstance()->display_metadatas_[display].k_screen[0],
                    ModelManager::GetInstance()->display_metadatas_[display].k_screen[4]};
        float cc[2]{ModelManager::GetInstance()->display_metadatas_[display].k_screen[2],
                    ModelManager::GetInstance()->display_metadatas_[display].k_screen[5]};
        HERON_LOG_DEBUG("{} fc:{:.4f},{:.4f}, cc:{:.4f},{:.4f}", display == 0 ? "left" : "right", fc[0], fc[1], cc[0], cc[1]);
        s_gdc_warp[display] = GDCQuadWarp::Create();
        s_gdc_warp[display]->WarpInit(fc, cc, dp_src_size_pixel, s_test_case.quad_size_meters, s_test_case.quad_to_world);
    }
}

static bool PopulateFrameMetadata()
{
    std::string glasses_config_json_string;
    if (!heron::ReadLocalTextFile(s_test_case.glasses_config_full_path, glasses_config_json_string))
        return false;
    if (!ParseGlassesConfig(glasses_config_json_string.c_str(), glasses_config_json_string.size()))
        return false;
    // uint32_t mesh_cols = DIV_CEIL(s_in_buffer.width, ROW_COUNT_IN_BLOCK) + 1;
    uint32_t mesh_rows = DIV_CEIL(s_in_buffer.height, ROW_COUNT_IN_BLOCK) + 1;

    InitWarppers();
    HERON_LOG_INFO("warppers inited. going to load head transforms");

    std::vector<Transform> transforms;
    LoadFrameHeadTransforms(s_test_case.head_transforms_file_path, transforms);
    HERON_LOG_INFO("head transforms loaded (count:{}). going to populate frame metadata", transforms.size());
    for (int display = 0; display < DISPLAY_USAGE_COUNT; ++display)
    {
        s_gdc_init_config[display].padding_color[0] = 0;
        s_gdc_init_config[display].padding_color[1] = 0;
        s_gdc_init_config[display].padding_color[2] = 0;
        s_gdc_init_config[display].padding_color[3] = 0;

        uint32_t mesh_cols = DIV_CEIL(s_in_buffer.width, ROW_COUNT_IN_BLOCK) + 1;
        uint32_t mesh_rows = DIV_CEIL(s_in_buffer.height, ROW_COUNT_IN_BLOCK) + 1;
        Transform eye_from_head = display == 0 ? s_test_case.left_display_to_head : s_test_case.right_display_to_head;
        for (int i = 0; i < transforms.size(); ++i)
        {
            Transform head_transform;
            heron::warp::TransformToEyePose(transforms[i], eye_from_head, head_transform);
            PrintObject("mesh_row_" + std::to_string(i), head_transform);
            float matrix33[9];
            GenGDCWarpMatrix(matrix33, head_transform);
            for (uint32_t j = 0; j < mesh_cols; ++j)
                memcpy(((char *)s_warp_virtual_addr[display]) + (i * mesh_cols + j) * sizeof(matrix33), (const char *)(&matrix33[0]), sizeof(matrix33));
        }
        s_gdc_init_config[display].warp_mode = 0; // 0: use warp.dat file; 1 use apb matrix; 2 disable
        s_gdc_init_config[display].warp_flush_cnt = 1;

        uint32_t count = 0;
        std::vector<float> data(mesh_cols * mesh_rows * 4, 0.0f);
        HERON_LOG_INFO("data size:{}", data.size());
        if (!ParseComponentDisplayDistortionDataFromGlassesConfig(glasses_config_json_string, display == 0 ? NR_COMPONENT_DISPLAY_LEFT : NR_COMPONENT_DISPLAY_RIGHT, data.data(), count))
            return NR_PLUGIN_RESULT_FAILURE;
        HERON_LOG_INFO("{} distortion data size:{}", display == 0 ? "left" : "right", count);
        for (int i = 0; i < count; i += 4)
        {
            ((float *)s_mesh_virtual_addr[display])[i / 2] = data[i + 2];
            ((float *)s_mesh_virtual_addr[display])[i / 2 + 1] = data[i + 3];
        }
        s_gdc_init_config[display].mesh_mode = 0;                               // 0:32x32 blocking 3:disable
        s_gdc_init_config[display].mesh_stride = mesh_cols * 2 * sizeof(float); // mesh每行1920/32=61个点,每个点两个float,61x8bytes=488

        GenGDCWeight(COLUMN_COUNT_IN_BLOCK, ROW_COUNT_IN_BLOCK, (float *)s_weight_virtual_addr[display]);
        s_gdc_init_config[display].weight_mode = 1; // 0:inner bi-linear; 1: weight.dat file
    }
    return true;
}

static bool TweakGDCConfigToUseMeshOnly(int display)
{
    s_gdc_init_config[display].warp_mode = 2; // 0: use warp.dat file; 1 use apb matrix; 2 disable

    uint32_t mesh_cols = DIV_CEIL(s_in_buffer.width, ROW_COUNT_IN_BLOCK) + 1;
    uint32_t mesh_rows = DIV_CEIL(s_in_buffer.height, ROW_COUNT_IN_BLOCK) + 1;
    for (int i = 0; i < mesh_cols * mesh_rows; ++i)
    {
        Mat3f warp_matrix_gdc33 = Mat3f::Identity();
        for (int j = 0; j < 9; j++) // seems has better performance than Eigen::Map<Eigen::Matrix<float, 3, 3, Eigen::RowMajor>>
            warp_matrix_gdc33(j / 3, j % 3) = ((float *)s_warp_virtual_addr[display])[i * 9 + j];
        Vector3f tmp(((float *)s_mesh_virtual_addr[display])[i * 2], ((float *)s_mesh_virtual_addr[display])[i * 2 + 1], 1.0f);
        Vector3f tmp_tmp = warp_matrix_gdc33 * tmp;
        HERON_LOG_DEBUG("mesh{}:({:.4f},{:.4f}) -> ({:.4f}, {:.4f}, {:.4f}) -> ({:.4f},{:.4f})",
                        display, tmp.x(), tmp.y(),
                        tmp_tmp.x(), tmp_tmp.y(), tmp_tmp.z(),
                        tmp_tmp.x() / tmp_tmp.z(), tmp_tmp.y() / tmp_tmp.z());
        ((float *)s_mesh_virtual_addr[display])[i * 2] = tmp_tmp.x() / tmp_tmp.z();
        ((float *)s_mesh_virtual_addr[display])[i * 2 + 1] = tmp_tmp.y() / tmp_tmp.z();
    }

    return true;
}

int main(int argc, char **argv)
{
    if (argc != 2)
    {
        HERON_LOG_ERROR("usage: {} <app_gdc_file_in_file_out.json>", argv[0]);
        exit(1);
    }

    std::string test_case_json_string;
    if (!heron::ReadLocalTextFile(argv[1], test_case_json_string))
        return false;
    if (!PopulateTestCase(test_case_json_string))
    {
        HERON_LOG_ERROR("Failed to populate test case");
        exit(1);
    }

    DispatcherWrapper::GetInstance()->LoadLibraries();

    HERON_LOG_INFO("LoadLibraries done. going to populate input&output frame");
    if (!PopulateInOutFrame(s_test_case.src_img_full_path))
    {
        HERON_LOG_ERROR("Failed to populate frame");
        exit(1);
    }
    HERON_LOG_INFO("input frame populated. going to allocate mmz buffers");
    if (!AllocMmzBuffers(s_in_buffer.width, s_in_buffer.height))
    {
        HERON_LOG_ERROR("Failed to allocate mmz buffers");
        exit(1);
    }
    HERON_LOG_INFO("mmz buffers allocated. going to prepare GDC metadata");
    if (!PopulateFrameMetadata())
    {
        HERON_LOG_ERROR("Failed to populate frame metadata");
        exit(1);
    }
    HERON_LOG_INFO("GDC metadata prepared. going to process with GDC");

    // Generate a filename based on the current time
    char timestamp[64];
    time_t now = time(nullptr);
    struct tm *tm_info = localtime(&now);
    strftime(timestamp, sizeof(timestamp), "%H%M%S", tm_info);

    DispatcherWrapper::GetInstance()->GdcRenderToMmz(s_gdc_init_config[DISPLAY_USAGE_LEFT], s_in_buffer, s_out_buffer);
    HERON_LOG_INFO("GDCProcess for left_eye done. going to dump output frame");
    std::string file_full_path = "./output_" + std::string(timestamp) + "_left.png";
    DumpFrameAsPNG(file_full_path, s_out_buffer);

    DispatcherWrapper::GetInstance()->GdcRenderToMmz(s_gdc_init_config[DISPLAY_USAGE_RIGHT], s_in_buffer, s_out_buffer);
    HERON_LOG_INFO("GDCProcess for right_eye done. going to dump output frame");
    file_full_path = "./output_" + std::string(timestamp) + "_right.png";
    DumpFrameAsPNG(file_full_path, s_out_buffer);

    if (s_test_case.do_tweak_mesh)
    {
        TweakGDCConfigToUseMeshOnly(DISPLAY_USAGE_LEFT);
        DispatcherWrapper::GetInstance()->GdcRenderToMmz(s_gdc_init_config[DISPLAY_USAGE_LEFT], s_in_buffer, s_out_buffer);
        HERON_LOG_INFO("GDCProcess for left_eye_tweaked done. going to dump output frame");
        file_full_path = "./output_" + std::string(timestamp) + "_left_tweaked.png";
        DumpFrameAsPNG(file_full_path, s_out_buffer);

        TweakGDCConfigToUseMeshOnly(DISPLAY_USAGE_RIGHT);
        DispatcherWrapper::GetInstance()->GdcRenderToMmz(s_gdc_init_config[DISPLAY_USAGE_RIGHT], s_in_buffer, s_out_buffer);
        HERON_LOG_INFO("GDCProcess for right_eye_tweaked done. going to dump output frame");
        file_full_path = "./output_" + std::string(timestamp) + "_right_tweaked.png";
        DumpFrameAsPNG(file_full_path, s_out_buffer);
    }
    ReleaseMmzBuffers();
    HERON_LOG_INFO("ALL PASS!");
    return 0;
}