#include <heron/util/warp.h>
#include <heron/util/log.h>

#include <warpcore/warp_interfaces.h>

using namespace heron;
using namespace heron::warp;

// quad_size_x: 1.7777778 y: 1

// inputs
static Vector3f s_quad_position(0, 0, -3);
static Quatf s_quad_rotation(1, 0, 0, 0); // watch the order: [w, x, y, z]
static float s_k_screen[9] = {
    2697.62,
    0.0,
    957.451,
    0.0,
    2671.09,
    537.519,
    0.0,
    0.0,
    1.0};
static float s_left_tan = -0.35492432;
static float s_right_tan = 0.35681415;
static float s_top_tan = 0.20123582;
static float s_bottom_tan = -0.2030935;
static float s_zn = 0.3;
static float s_zf = 100.0;
static Vector3f s_new_pose_position(-0.045154784, 0.058474857, -0.056850716);
static Quatf s_new_pose_rotation(0.9915057, -0.048350833, 0.11949474, -0.017312935);
static Vector2i s_quad_size_pixel(1920, 1080);
static Vector2i s_screen_size_pixel(1920, 1080);
static Vector2f s_quad_size_meters(1.7777778, 1.0);
// static ImgSize2f s_quad_size_meters(1.0, 1.0);

bool CalculatePlaneMatrix(const Transform &w_T_plane, const Transform &w_T_head, Mat4f &plane_matrix)
{
    Mat4f w_W_plane = ConvertTransformToMat4f(w_T_plane);
    Mat4f w_W_head = ConvertTransformToMat4f(w_T_head);
    Mat4f pose_change_matrix = w_W_head.inverse() * w_W_plane;
    Vector4f point_4f = Vector4f(1.0f, 1.0f, 0.0f, 1.0f);
    Vector4f normal_4f = Vector4f(0.0f, 0.0f, 1.0f, 0.0f);

    point_4f = pose_change_matrix * point_4f;
    normal_4f = pose_change_matrix * normal_4f;
    normal_4f.normalize();
    Vector3f plane_point = point_4f.head<3>();
    Vector3f plane_normal = normal_4f.head<3>();

    // WARP_CORE_LOG_INFO("CalculatePlaneMatrix point:{} {} {} {}", point_4f.x(), point_4f.y(), point_4f.z(), point_4f.w());
    // WARP_CORE_LOG_INFO("CalculatePlaneMatrix plane_point:{} {} {}", plane_point.x(), plane_point.y(), plane_point.z());
    // WARP_CORE_LOG_INFO("CalculatePlaneMatrix normal:{} {} {} {}", normal_4f.x(), normal_4f.y(), normal_4f.z(), normal_4f.w());
    float plane_distance = plane_normal.dot(plane_point);
    float cross_point_length = point_4f.head<3>().norm();
    // plane_point.normalize();
    // float plane_distance_nor2 = plane_point.dot(plane_normal);
    float plane_distance_nor = plane_distance / cross_point_length;
    HERON_LOG_INFO("cross point: {} {} {}, plane distance nor is: {}",
                   plane_point.x(), plane_point.y(), plane_point.z(), plane_distance_nor);
    // should normalize the plane_point first

    // If the vector decided by original point and the plane point is close to the plane normal, which means the plane will degenerate to be a line on near clip plane.
    // In other words, the original point is on the plane, the distance from original point to the plane is zero.
    // while the plane matrix need to devide by the distance from original point to the plane, if the divider is zero, things may happen out of control.
    // So, try to avoid this case to happen, use the default plane to do plane time warp.
    // if (fabs(plane_distance_nor) < 0.05)
    ///{
    ///    HERON_LOG_INFO("plane is not valid, back to defalt plane.");
    ///    return false;
    ///}

    // WARP_CORE_LOG_INFO("plane info is : {} {} {} {} distance is: {}",
    //  plane_normal.x(), plane_normal.y(), plane_normal.z(), plane_point.z(), plane_distance);
    plane_matrix = Mat4f::Identity();
    plane_matrix(3, 0) = plane_normal.x() / plane_distance;
    plane_matrix(3, 1) = plane_normal.y() / plane_distance;
    plane_matrix(3, 2) = plane_normal.z() / plane_distance;
    plane_matrix(3, 3) = 0.0f;
    return true;
}
int main(int argc, char **argv)
{
    Transform plane_transform;
    plane_transform.position = Vector3f(0, 0, -4.0);
    // 0.0, 0.6755902, 0.0, 0.7372773
    plane_transform.rotation = Quatf(0.7372773, 0.0, 0.6755902, 0.0);

    Transform head_transform;
    head_transform.position = Vector3f(0, 0, 0);
    head_transform.rotation = Quatf(1.0, 0.0, 0.0, 0.0);

    Mat4f plane_matrix;
    if (!CalculatePlaneMatrix(plane_transform, head_transform, plane_matrix))
    {
        HERON_LOG_INFO("CalculatePlaneMatrix error");
    }
    HERON_LOG_DEBUG("----------------------------------------------------");
    PrintObject("plane_matrix", plane_matrix);

    Mat4f projection = GetProjectionMatrixFromFov(s_left_tan, s_right_tan, s_top_tan, s_bottom_tan, s_zn, s_zf);
    Mat4f projection_inv = projection.inverse();
    HERON_LOG_DEBUG("----------------------------------------------------");
    PrintObject("projection inv", projection_inv);

    Mat4f final_matrix = plane_matrix * projection.inverse();
    HERON_LOG_DEBUG("----------------------------------------------------");
    PrintObject("final_matrix", final_matrix);

    Vector4f tmp_vec4(0, 0, 0, 1);
    Vector4f tmp_vec4_result = projection * tmp_vec4;
    HERON_LOG_DEBUG("----------------------------------------------------");
    PrintObject("tmp_vec4", tmp_vec4);
    PrintObject("tmp_vec4_result", tmp_vec4_result);
    Vector4f tmp_vec4_reverse = projection.inverse() * tmp_vec4_result;
    PrintObject("tmp_vec4_reverse", tmp_vec4_reverse);

    Vector4f dest_point_clip(0, 0, -0.3, 0.3);
    Vector4f point_3D_head_view = projection.inverse() * dest_point_clip;
    HERON_LOG_DEBUG("----------------------------------------------------");
    PrintObject("point_3D_head_view1", point_3D_head_view);
    Vector4f src_point_in_head_view = final_matrix * dest_point_clip;
    HERON_LOG_DEBUG("----------------------------------------------------");
    PrintObject("src_point_in_head_view1", src_point_in_head_view);

    HERON_LOG_INFO("ALL PASS!");
    framework::util::log::Logger::shutdown();
    return 0;
}
