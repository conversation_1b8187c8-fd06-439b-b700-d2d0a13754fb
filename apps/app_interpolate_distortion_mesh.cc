/**
 * @file app_interpolate_distortion_mesh.cc
 * @brief This file contains main function to do unit test
 *
 * This program tests pixel-wise interpolation of distortion mesh
 *
 * <AUTHOR> <EMAIL>
 * @date 05/28/2025
 */
#include <heron/util/log.h>

#include <framework/util/json.h>

#include <iostream>
#include <fstream>

using namespace std;

// Structure to hold mesh data for interpolation
struct MeshData
{
    int num_rows;
    int num_cols;
    vector<float> grid_x;
    vector<float> grid_y;
    vector<float> pt_x;
    vector<float> pt_y;
};

// Function to generate pixel-wise distortion values
static bool GeneratePixelwiseDistortion(const MeshData &mesh,
                                        int display_width, int display_height,
                                        vector<float> &pixel_dis_pt_x,
                                        vector<float> &pixel_dis_pt_y)
{

    if (mesh.num_rows < 2 || mesh.num_cols < 2)
    {
        HERON_LOG_ERROR("Mesh must have at least 2x2 grid points");
        return false;
    }

    // Resize output vectors
    pixel_dis_pt_x.resize(display_width * display_height);
    pixel_dis_pt_y.resize(display_width * display_height);

    // Find mesh bounds
    float min_grid_x = *min_element(mesh.grid_x.begin(), mesh.grid_x.end());
    float max_grid_x = *max_element(mesh.grid_x.begin(), mesh.grid_x.end());
    float min_grid_y = *min_element(mesh.grid_y.begin(), mesh.grid_y.end());
    float max_grid_y = *max_element(mesh.grid_y.begin(), mesh.grid_y.end());

    HERON_LOG_INFO("Mesh bounds: x[{:.2f}, {:.2f}], y[{:.2f}, {:.2f}]",
                   min_grid_x, max_grid_x, min_grid_y, max_grid_y);

    // Generate distortion for each pixel
    for (int pixel_y = 0; pixel_y < display_height; ++pixel_y)
    {
        for (int pixel_x = 0; pixel_x < display_width; ++pixel_x)
        {
            int pixel_idx = pixel_y * display_width + pixel_x;

            // Map pixel coordinates to mesh grid coordinates
            float grid_x = min_grid_x + (float)pixel_x * (max_grid_x - min_grid_x) / (display_width - 1);
            float grid_y = min_grid_y + (float)pixel_y * (max_grid_y - min_grid_y) / (display_height - 1);

            // Find the mesh cell containing this pixel
            int mesh_col = -1, mesh_row = -1;
            float cell_x = -1, cell_y = -1;

            // Find which mesh cell this pixel belongs to
            for (int row = 0; row < mesh.num_rows - 1; ++row)
            {
                for (int col = 0; col < mesh.num_cols - 1; ++col)
                {
                    int idx_tl = row * mesh.num_cols + col;             // top-left
                    int idx_tr = row * mesh.num_cols + (col + 1);       // top-right
                    int idx_bl = (row + 1) * mesh.num_cols + col;       // bottom-left
                    int idx_br = (row + 1) * mesh.num_cols + (col + 1); // bottom-right

                    float x1 = mesh.grid_x[idx_tl];
                    float x2 = mesh.grid_x[idx_tr];
                    float y1 = mesh.grid_y[idx_tl];
                    float y2 = mesh.grid_y[idx_bl];

                    if (grid_x >= x1 && grid_x <= x2 && grid_y >= y1 && grid_y <= y2)
                    {
                        mesh_row = row;
                        mesh_col = col;
                        cell_x = (grid_x - x1) / (x2 - x1);
                        cell_y = (grid_y - y1) / (y2 - y1);
                        break;
                    }
                }
                if (mesh_row >= 0)
                    break;
            }

            if (mesh_row < 0 || mesh_col < 0)
            {
                // Pixel is outside mesh bounds, use nearest neighbor
                float min_dist = numeric_limits<float>::max();
                int nearest_idx = 0;

                for (int i = 0; i < mesh.num_rows * mesh.num_cols; ++i)
                {
                    float dx = grid_x - mesh.grid_x[i];
                    float dy = grid_y - mesh.grid_y[i];
                    float dist = dx * dx + dy * dy;
                    if (dist < min_dist)
                    {
                        min_dist = dist;
                        nearest_idx = i;
                    }
                }

                pixel_dis_pt_x[pixel_idx] = mesh.pt_x[nearest_idx];
                pixel_dis_pt_y[pixel_idx] = mesh.pt_y[nearest_idx];
            }
            else
            {
                // Bilinear interpolation within the mesh cell
                int idx_tl = mesh_row * mesh.num_cols + mesh_col;             // top-left
                int idx_tr = mesh_row * mesh.num_cols + (mesh_col + 1);       // top-right
                int idx_bl = (mesh_row + 1) * mesh.num_cols + mesh_col;       // bottom-left
                int idx_br = (mesh_row + 1) * mesh.num_cols + (mesh_col + 1); // bottom-right

                // Interpolate dis_pt_x
                float pt_x_tl = mesh.pt_x[idx_tl];
                float pt_x_tr = mesh.pt_x[idx_tr];
                float pt_x_bl = mesh.pt_x[idx_bl];
                float pt_x_br = mesh.pt_x[idx_br];

                float pt_x_top = pt_x_tl + (pt_x_tr - pt_x_tl) * cell_x;
                float pt_x_bottom = pt_x_bl + (pt_x_br - pt_x_bl) * cell_x;
                pixel_dis_pt_x[pixel_idx] = pt_x_top + (pt_x_bottom - pt_x_top) * cell_y;

                // Interpolate dis_pt_y
                float pt_y_tl = mesh.pt_y[idx_tl];
                float pt_y_tr = mesh.pt_y[idx_tr];
                float pt_y_bl = mesh.pt_y[idx_bl];
                float pt_y_br = mesh.pt_y[idx_br];

                float pt_y_top = pt_y_tl + (pt_y_tr - pt_y_tl) * cell_x;
                float pt_y_bottom = pt_y_bl + (pt_y_br - pt_y_bl) * cell_x;
                pixel_dis_pt_y[pixel_idx] = pt_y_top + (pt_y_bottom - pt_y_top) * cell_y;
            }
        }
    }

    HERON_LOG_INFO("Generated pixel-wise distortion for {}x{} display", display_width, display_height);
    return true;
}

// Helper function to extract mesh data from JSON
static bool ExtractMeshDataFromJson(const Json::Value &display_data, MeshData &mesh)
{
    if (!display_data.isMember("num_row") || !display_data.isMember("num_col") || !display_data.isMember("data"))
    {
        HERON_LOG_ERROR("Missing required mesh data fields");
        return false;
    }

    mesh.num_rows = display_data["num_row"].asInt();
    mesh.num_cols = display_data["num_col"].asInt();

    Json::Value mesh_data = display_data["data"];
    int expected_size = mesh.num_rows * mesh.num_cols * 4;
    if ((int)mesh_data.size() != expected_size)
    {
        HERON_LOG_ERROR("Mesh data size mismatch. Expected: {}, Got: {}", expected_size, (int)mesh_data.size());
        return false;
    }

    // Resize vectors
    int total_points = mesh.num_rows * mesh.num_cols;
    mesh.grid_x.resize(total_points);
    mesh.grid_y.resize(total_points);
    mesh.pt_x.resize(total_points);
    mesh.pt_y.resize(total_points);

    // Extract mesh data
    for (int i = 0; i < total_points; ++i)
    {
        mesh.grid_x[i] = mesh_data[4 * i + 0].asFloat();
        mesh.grid_y[i] = mesh_data[4 * i + 1].asFloat();
        mesh.pt_x[i] = mesh_data[4 * i + 2].asFloat();
        mesh.pt_y[i] = mesh_data[4 * i + 3].asFloat();
    }

    HERON_LOG_INFO("Extracted mesh data: {}x{} grid points", mesh.num_rows, mesh.num_cols);
    return true;
}

// Example usage function
static bool GeneratePixelwiseDistortionFromConfig(const string &config_path,
                                                  int display_width, int display_height,
                                                  MeshData &left_mesh, MeshData &right_mesh,
                                                  vector<float> &left_dis_pt_x,
                                                  vector<float> &left_dis_pt_y,
                                                  vector<float> &right_dis_pt_x,
                                                  vector<float> &right_dis_pt_y)
{

    // Parse JSON config
    ifstream file(config_path, ios::binary | ios::ate);
    if (!file.is_open())
    {
        HERON_LOG_ERROR("Failed to open config file: {}", config_path);
        return false;
    }

    streamsize size = file.tellg();
    file.seekg(0, ios::beg);

    vector<char> buffer(size);
    if (!file.read(buffer.data(), size))
    {
        HERON_LOG_ERROR("Failed to read config file: {}", config_path);
        return false;
    }

    Json::Value json_root;
    Json::CharReaderBuilder json_builder;
    json_builder["collectComments"] = false;
    JSONCPP_STRING json_errs;
    istringstream json_stream(string(buffer.data(), static_cast<uint32_t>(size)));
    if (!parseFromStream(json_builder, json_stream, &json_root, &json_errs))
    {
        HERON_LOG_ERROR("Parse config error: {}", json_errs.c_str());
        return false;
    }

    if (!json_root.isMember("display_distortion"))
    {
        HERON_LOG_ERROR("No display_distortion in config");
        return false;
    }

    const Json::Value &distortion_data = json_root["display_distortion"];

    // Extract left display mesh
    if (!distortion_data.isMember("left_display"))
    {
        HERON_LOG_ERROR("No left_display in display_distortion");
        return false;
    }

    if (!ExtractMeshDataFromJson(distortion_data["left_display"], left_mesh))
    {
        HERON_LOG_ERROR("Failed to extract left display mesh data");
        return false;
    }

    // Extract right display mesh
    if (!distortion_data.isMember("right_display"))
    {
        HERON_LOG_ERROR("No right_display in display_distortion");
        return false;
    }

    if (!ExtractMeshDataFromJson(distortion_data["right_display"], right_mesh))
    {
        HERON_LOG_ERROR("Failed to extract right display mesh data");
        return false;
    }

    // Generate pixel-wise distortion for both displays
    if (!GeneratePixelwiseDistortion(left_mesh, display_width, display_height, left_dis_pt_x, left_dis_pt_y))
    {
        HERON_LOG_ERROR("Failed to generate left display pixel-wise distortion");
        return false;
    }

    if (!GeneratePixelwiseDistortion(right_mesh, display_width, display_height, right_dis_pt_x, right_dis_pt_y))
    {
        HERON_LOG_ERROR("Failed to generate right display pixel-wise distortion");
        return false;
    }

    HERON_LOG_INFO("Successfully generated pixel-wise distortion for both displays");
    return true;
}

static void GenerateMask(vector<char> &mask, const MeshData &mesh, vector<float> &dis_pt_x, vector<float> &dis_pt_y,
                         int display_width, int display_height)
{
    float min_x = 0.0f;
    float max_x = 1920.0f;
    float min_y = 6.0f;
    float max_y = 1080.0f;
    for (int col = 0; col < mesh.num_cols; ++col)
    {
        HERON_LOG_INFO("mesh_row: {}, mesh_col: {} [{},{}]", 0, col, mesh.pt_x[col], mesh.pt_y[col]);
        min_y = max(min_y, mesh.pt_y[col]);
    }
    for (int col = 0; col < mesh.num_cols; ++col)
    {
        int row = mesh.num_rows - 1;
        HERON_LOG_INFO("mesh_row: {}, mesh_col: {} [{},{}]", row, col, mesh.pt_x[row * mesh.num_cols + col], mesh.pt_y[row * mesh.num_cols + col]);
        max_y = min(max_y, mesh.pt_y[row * mesh.num_cols + col]);
    }
    for (int row = 0; row < mesh.num_rows; ++row)
    {
        HERON_LOG_INFO("mesh_row: {}, mesh_col: {} [{},{}]", row, 0, mesh.pt_x[row * mesh.num_cols], mesh.pt_y[row * mesh.num_cols]);
        min_x = max(min_x, mesh.pt_x[row * mesh.num_cols]);
    }
    for (int row = 0; row < mesh.num_rows; ++row)
    {
        int col = mesh.num_cols - 1;
        HERON_LOG_INFO("mesh_row: {}, mesh_col: {} [{},{}]", row, col, mesh.pt_x[row * mesh.num_cols + col], mesh.pt_y[row * mesh.num_cols + col]);
        max_x = min(max_x, mesh.pt_x[row * mesh.num_cols + col]);
    }
    HERON_LOG_INFO("min_x:{}, max_x:{}, min_y:{}, max_y:{}", min_x, max_x, min_y, max_y);
    for (int row = 0; row < display_height; ++row)
    {
        for (int col = 0; col < display_width; ++col)
        {
            int idx = (row * display_width + col);
            if (dis_pt_x[idx] < min_x || dis_pt_x[idx] > max_x || dis_pt_y[idx] < min_y || dis_pt_y[idx] > max_y)
            {
                float x_diff = 0.0f;
                float y_diff = 0.0f;
                if (dis_pt_x[idx] < min_x)
                    x_diff = min_x - dis_pt_x[idx];
                else if (dis_pt_x[idx] > max_x)
                    x_diff = dis_pt_x[idx] - max_x;
                if (dis_pt_y[idx] < min_y)
                    y_diff = min_y - dis_pt_y[idx];
                else if (dis_pt_y[idx] > max_y)
                    y_diff = dis_pt_y[idx] - max_y;
                int alpha = sqrt((x_diff * x_diff + y_diff * y_diff)) * 15; // diff * 0x0F
                mask.push_back(min(15, alpha));
            }
            else
                mask.push_back(0);
        }
    }
}

// Example usage of the pixel-wise distortion generation functions
int main()
{
    // Example 1: Using the complete function with config file
    vector<float> left_dis_pt_x, left_dis_pt_y;
    vector<float> right_dis_pt_x, right_dis_pt_y;
    MeshData left_mesh, right_mesh;

    int display_width = 1920;
    int display_height = 1080;
    string config_path = "glasses_config.json";

    if (GeneratePixelwiseDistortionFromConfig(config_path, display_width, display_height,
                                              left_mesh, right_mesh,
                                              left_dis_pt_x, left_dis_pt_y,
                                              right_dis_pt_x, right_dis_pt_y))
    {
        cout << "Successfully generated pixel-wise distortion!" << endl;
        cout << "Left display: " << left_dis_pt_x.size() << " pixels" << endl;
        cout << "Right display: " << right_dis_pt_x.size() << " pixels" << endl;

        // Example: Access distortion for a specific pixel (x=100, y=200)
        for (int pixel_y = 0; pixel_y < display_height; pixel_y += 100)
        {
            for (int pixel_x = 0; pixel_x < display_width; pixel_x += 100)
            {
                int pixel_idx = pixel_y * display_width + pixel_x;
                cout << "Left display pixel (" << pixel_x << ", " << pixel_y << ") -> "
                     << "distorted (" << left_dis_pt_x[pixel_idx] << ", "
                     << left_dis_pt_y[pixel_idx] << ")" << endl;

                cout << "Right display pixel (" << pixel_x << ", " << pixel_y << ") -> "
                     << "distorted (" << right_dis_pt_x[pixel_idx] << ", "
                     << right_dis_pt_y[pixel_idx] << ")" << endl;
            }
        }
        vector<char> left_mask;
        GenerateMask(left_mask, left_mesh, left_dis_pt_x, left_dis_pt_y, display_width, display_height);
        ofstream outFile("left_mask.bin", ios::binary);
        if (!outFile)
        {
            HERON_LOG_ERROR("Failed to open file for writing!");
            return 1;
        }
        outFile.write(reinterpret_cast<const char *>(left_mask.data()), left_mask.size() * sizeof(char));
        outFile.close();

        vector<char> right_mask;
        GenerateMask(right_mask, right_mesh, right_dis_pt_x, right_dis_pt_y, display_width, display_height);
        outFile = ofstream("right_mask.bin", ios::binary);
        if (!outFile)
        {
            HERON_LOG_ERROR("Failed to open file for writing!");
            return 1;
        }
        outFile.write(reinterpret_cast<const char *>(right_mask.data()), right_mask.size() * sizeof(char));
        outFile.close();
    }
    else
    {
        cout << "Failed to generate pixel-wise distortion!" << endl;
        return -1;
    }

    return 0;
}
