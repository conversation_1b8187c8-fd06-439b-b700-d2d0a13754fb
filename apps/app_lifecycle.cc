/**
 * @file app_lifecycle.cc
 * @brief This file contains main function to do unit test
 *
 * This program mainly tests lifecycle apis of flinger plugin.
 * specifically:
 * NRPluginResult HeronRegister( NRPluginHandle handle );
 * NRPluginResult HeronUnregister( NRPluginHandle handle );
 * NRPluginResult HeronInitialize( NRPluginHandle handle );
 * NRPluginResult HeronStart( NRPluginHandle handle );
 * NRPluginResult HeronPause( NRPluginHandle handle );
 * NRPluginResult HeronResume( NRPluginHandle handle );
 * NRPluginResult HeronStop( NRPluginHandle handle );
 * NRPluginResult HeronRelease( NRPluginHandle handle );
 *
 * <AUTHOR>
 * @date 04/10/2024
 */
#define NRPLUGIN // in order to use NR defined types

#include <heron/interface/include/public/nr_plugin_lifecycle.h>
#include <heron/interface/include/flinger/nr_plugin_flinger.h>
// #include <heron/dispatch/dispatcher.h>
// dispatcher.h includes header files such as nr_extra_dp.h which belone to
// another set of interfaces (heron/interface/device_api v.s. heron/interface/include).
// files from these two sets of interfaces are not supposed to be included in the same file.
// so here we have to use dispatcher_wrapper.h instead of dispatcher.h
#include <heron/dispatch/dispatcher_wrapper.h>
#include <heron/util/log.h>

#include <framework/util/dlutil.h>

#include <lifecycle_common_macro.h>

/// global variables
extern NRPluginHandle heron_g_handle;
extern NRInterfaces g_interfaces;
extern NRPluginLifecycleProvider g_flinger_lifecycle_provider;
extern NRFlingerProvider g_flinger_provider;

static void CheckProviderApiExistance()
{
    CHECK_EXISTANCE(g_flinger_provider, StartDpRender);
    CHECK_EXISTANCE(g_flinger_provider, StopDpRender);
}

int main(int argc, char **argv)
{
    HERON_LOG_INFO("testing NRPluginLifecycleProvider apis ...");

    NRPluginLoad_FLINGER(&g_interfaces);

    NRPluginHandle plugin_handle = 3;

    CALL_LIFECYCLE_FUNC(Register)
    HERON_LOG_INFO("heron_g_handle after register: {}", heron_g_handle);
    CALL_LIFECYCLE_FUNC(Initialize)
    CALL_LIFECYCLE_FUNC(Start)
    heron::dispatch::DispatcherWrapper::GetInstance()->EnableDisplay();
    uint32_t rounds = 0;
    NRResolutionInfo resolution_info{1920, 1080, 90};
    for (uint32_t i = 0; i < rounds; i++)
    {
        PLUGIN_API_ABORT_ON_ERROR(g_flinger_provider.StartDpRender(heron_g_handle, &resolution_info), "StartDpRender");
        HERON_LOG_INFO("StartDpRender success {}/{}.", i, rounds);
        usleep(2 * 1000 * 1000);
        PLUGIN_API_ABORT_ON_ERROR(g_flinger_provider.StopDpRender(heron_g_handle), "StopDpRender");
        HERON_LOG_INFO("StopDpRender success {}/{}.", i, rounds);
        usleep(1 * 1000 * 1000);
    }
    PLUGIN_API_ABORT_ON_ERROR(g_flinger_provider.StartDpRender(heron_g_handle, &resolution_info), "StartDpRender");
    usleep(300 * 1000 * 1000); // let stub Dp thread and Dpu threads run for a while
    CALL_LIFECYCLE_FUNC(Update)
    CALL_LIFECYCLE_FUNC(Pause)
    CALL_LIFECYCLE_FUNC(Resume)
    CALL_LIFECYCLE_FUNC(Stop)
    CALL_LIFECYCLE_FUNC(Unregister)
    CALL_LIFECYCLE_FUNC(Release)

    NRPluginUnload_FLINGER();
    HERON_LOG_INFO("test NRPluginLifecycleProvider apis done.");
    framework::util::log::Logger::shutdown();
    return 0;
}