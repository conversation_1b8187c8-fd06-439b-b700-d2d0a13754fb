#define NRPLUGIN // in order to use NR defined types

#include <apps/stubs/util.h>

#include <heron/interface/include/common/nr_plugin_hmd.h>
#include <heron/util/misc.h>
#include <heron/util/log.h>

#include <framework/util/json.h>

NRPluginResult GetComponentPoseFromHead(NRPluginHandle /*handle*/,
                                        NRComponent component,
                                        NRTransform *out_transform)
{
    out_transform->position.x = out_transform->position.y = out_transform->position.z = 0.0f;
    if (component == NR_COMPONENT_DISPLAY_LEFT)
    {
        out_transform->position.x = -0.032f;
    }
    if (component == NR_COMPONENT_DISPLAY_RIGHT)
    {
        out_transform->position.x = 0.032f;
    }
    out_transform->position.y = 0.0f;
    out_transform->position.z = 0.0f;
    out_transform->rotation.qx = out_transform->rotation.qy = out_transform->rotation.qz = 0.0f;
    out_transform->rotation.qw = 1.0f;
    return NR_PLUGIN_RESULT_SUCCESS;
}

static float left_display_fov[4] = {-0.39990422, 0.37266943, 0.22310436, -0.21339862};
static float right_display_fov[4] = {-0.3729752, 0.39857703, 0.22428653, -0.21179518};
NRPluginResult GetComponentFov(NRPluginHandle /*handle*/,
                               NRComponent component,
                               NRFov4f *out_fov)
{
    HERON_LOG_TRACE("calling stub {}", __func__);
    NRPluginResult ret = NR_PLUGIN_RESULT_FAILURE;
    switch (component)
    {
    case NR_COMPONENT_HEAD:
        out_fov->left_tan = (left_display_fov[0] + right_display_fov[0]) / 2;
        out_fov->right_tan = (left_display_fov[1] + right_display_fov[1]) / 2;
        out_fov->top_tan = (left_display_fov[2] + right_display_fov[2]) / 2;
        out_fov->bottom_tan = (left_display_fov[3] + right_display_fov[3]) / 2;
        ret = NR_PLUGIN_RESULT_SUCCESS;
        break;
    case NR_COMPONENT_DISPLAY_LEFT:
        out_fov->left_tan = left_display_fov[0];
        out_fov->right_tan = left_display_fov[1];
        out_fov->top_tan = left_display_fov[2];
        out_fov->bottom_tan = left_display_fov[3];
        ret = NR_PLUGIN_RESULT_SUCCESS;
        break;
    case NR_COMPONENT_DISPLAY_RIGHT:
        out_fov->left_tan = right_display_fov[0];
        out_fov->right_tan = right_display_fov[1];
        out_fov->top_tan = right_display_fov[2];
        out_fov->bottom_tan = right_display_fov[3];
        ret = NR_PLUGIN_RESULT_SUCCESS;
        break;
    default:
        break;
    }
    return ret;
}

const static std::string GLASSES_CONFIG_FILE_NAME = "glasses_config.json";
static std::string s_glasses_config_json_string = "";
NRPluginResult GetComponentDisplayDistortionSize(NRPluginHandle /*handle*/, NRComponent component, NRSize2i *distortion_size)
{
    if (s_glasses_config_json_string.empty())
    {
        if (!heron::ReadLocalTextFile(GLASSES_CONFIG_FILE_NAME, s_glasses_config_json_string))
            return NR_PLUGIN_RESULT_FAILURE;
    }
    Json::Value json_root;
    Json::CharReaderBuilder json_builder;
    json_builder["collectComments"] = false;
    JSONCPP_STRING json_errs;
    std::istringstream json_stream(s_glasses_config_json_string);
    if (!parseFromStream(json_builder, json_stream, &json_root, &json_errs))
    {
        HERON_LOG_ERROR("Parse glasses config error, json_errs = {}", json_errs.c_str());
        return NR_PLUGIN_RESULT_FAILURE;
    }
    const Json::Value &distortion_data = json_root["display_distortion"];
    if (!json_root.isMember("display_distortion"))
    {
        HERON_LOG_ERROR("No display_distortion in config");
        return NR_PLUGIN_RESULT_FAILURE;
    }
    int type = 0;
    if (component == NR_COMPONENT_DISPLAY_LEFT)
    {
        const Json::Value &left_distortion_data = distortion_data["left_display"];
        if (left_distortion_data.isMember("type"))
            type = left_distortion_data["type"].asInt();
        if (left_distortion_data.isMember("num_row"))
            distortion_size->height = left_distortion_data["num_row"].asInt();
        if (left_distortion_data.isMember("num_col"))
            distortion_size->width = left_distortion_data["num_col"].asInt();
        HERON_LOG_DEBUG("Parse distortion for left display type:{} cols:{} rows:{}.", type, distortion_size->width, distortion_size->height);
    }
    if (component == NR_COMPONENT_DISPLAY_RIGHT)
    {
        const Json::Value &right_distortion_data = distortion_data["right_display"];
        if (right_distortion_data.isMember("type"))
            type = right_distortion_data["type"].asInt();
        if (right_distortion_data.isMember("num_row"))
            distortion_size->height = right_distortion_data["num_row"].asInt();
        if (right_distortion_data.isMember("num_col"))
            distortion_size->width = right_distortion_data["num_col"].asInt();
        HERON_LOG_DEBUG("Parse distortion for right display type:{} cols:{} rows:{}.", type, distortion_size->width, distortion_size->height);
    }
    return NR_PLUGIN_RESULT_SUCCESS;
}

NRPluginResult GetComponentDisplayDistortionData(NRPluginHandle /*handle*/, NRComponent component, int size, float *data)
{
    if (s_glasses_config_json_string.empty())
    {
        if (!heron::ReadLocalTextFile(GLASSES_CONFIG_FILE_NAME, s_glasses_config_json_string))
            return NR_PLUGIN_RESULT_FAILURE;
    }
    uint32_t count = 0;
    if (!ParseComponentDisplayDistortionDataFromGlassesConfig(s_glasses_config_json_string, component, data, count))
        return NR_PLUGIN_RESULT_FAILURE;
    std::string tmp = component == NR_COMPONENT_DISPLAY_LEFT ? "left" : "right";
    if (count != size)
    {
        HERON_LOG_ERROR("{} distortion data size mismatch. expect:{} got:{}.", tmp, size, count);
        return NR_PLUGIN_RESULT_FAILURE;
    }
    return NR_PLUGIN_RESULT_SUCCESS;
}