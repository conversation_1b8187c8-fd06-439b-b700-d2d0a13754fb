#include <heron/interface/include/public/nr_plugin_types.h>
#include <heron/util/types.h>

#include <string>

bool ParseComponentDisplayDistortionDataFromGlassesConfig(const std::string &glasses_config_json_string,
                                                          NRComponent component,
                                                          float *data, uint32_t &count);

bool LoadFrameHeadTransforms(const std::string &file_path, std::vector<Transform> &transforms);