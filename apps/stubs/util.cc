#define NRPLUGIN // in order to use NR defined types

#include <apps/stubs/util.h>

#include <heron/util/log.h>

#include <framework/util/json.h>

#include <fstream>

bool ParseComponentDisplayDistortionDataFromGlassesConfig(const std::string &glasses_config_json_string,
                                                          NRComponent component,
                                                          float *data, uint32_t &count)
{
    Json::Value json_root;
    Json::CharReaderBuilder json_builder;
    json_builder["collectComments"] = false;
    JSONCPP_STRING json_errs;
    std::istringstream json_stream(glasses_config_json_string);
    if (!parseFromStream(json_builder, json_stream, &json_root, &json_errs))
    {
        HERON_LOG_ERROR("Parse glasses config error, json_errs = {}", json_errs.c_str());
        return false;
    }

    const Json::Value &distortion_data = json_root["display_distortion"];
    if (!json_root.isMember("display_distortion"))
    {
        HERON_LOG_ERROR("No display_distortion in config");
        return false;
    }

    int type = 0, num_rows = 0, num_cols = 0;
    if (component == NR_COMPONENT_DISPLAY_LEFT)
    {
        const Json::Value &left_distortion_data = distortion_data["left_display"];
        if (left_distortion_data.isMember("type"))
            type = left_distortion_data["type"].asInt();
        if (left_distortion_data.isMember("num_row"))
            num_rows = left_distortion_data["num_row"].asInt();
        if (left_distortion_data.isMember("num_col"))
            num_cols = left_distortion_data["num_col"].asInt();
        HERON_LOG_DEBUG("Parse distortion for left display type:{} rows:{} cols:{}.", type, num_rows, num_cols);
        Json::Value distortion_left_mesh_data = left_distortion_data["data"];
        int left_distortion_data_size = (int)distortion_left_mesh_data.size();
        if (left_distortion_data_size != num_rows * num_cols * 4)
        {
            HERON_LOG_ERROR("left distortion data size mismatch. expect{}x{}x4={} got:{}.", num_rows, num_cols, num_rows * num_cols * 4, left_distortion_data_size);
            return false;
        }
        for (int i = 0; i < left_distortion_data_size; ++i)
            data[i] = distortion_left_mesh_data[i].asFloat();
        count = left_distortion_data_size;
    }
    if (component == NR_COMPONENT_DISPLAY_RIGHT)
    {
        const Json::Value &right_distortion_data = distortion_data["right_display"];
        if (right_distortion_data.isMember("type"))
            type = right_distortion_data["type"].asInt();
        if (right_distortion_data.isMember("num_row"))
            num_rows = right_distortion_data["num_row"].asInt();
        if (right_distortion_data.isMember("num_col"))
            num_cols = right_distortion_data["num_col"].asInt();
        HERON_LOG_DEBUG("Parse distortion for right display type:{} rows:{} cols:{}.", type, num_rows, num_cols);
        Json::Value distortion_right_mesh_data = right_distortion_data["data"];
        int right_distortion_data_size = (int)distortion_right_mesh_data.size();
        if (right_distortion_data_size != num_rows * num_cols * 4)
        {
            HERON_LOG_ERROR("right distortion data size mismatch. expect{}x{}x4={} got:{}.", num_rows, num_cols, num_rows * num_cols * 4, right_distortion_data_size);
            return false;
        }
        for (int i = 0; i < right_distortion_data_size; ++i)
            data[i] = distortion_right_mesh_data[i].asFloat();
        count = right_distortion_data_size;
    }
    return true;
}

bool LoadFrameHeadTransforms(const std::string &file_path, std::vector<Transform> &transforms)
{
    std::ifstream file(file_path, std::ios::binary);
    if (!file.is_open())
    {
        HERON_LOG_ERROR("Failed to open file: {}", file_path);
        return false;
    }

    file.seekg(0, std::ios::end);
    size_t file_size = file.tellg();
    file.seekg(0, std::ios::beg);

    size_t num_transforms = file_size / sizeof(Transform);
    transforms.resize(num_transforms);

    file.read(reinterpret_cast<char *>(transforms.data()), file_size);
    file.close();

    return true;
}