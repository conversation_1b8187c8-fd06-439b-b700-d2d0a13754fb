import matplotlib.pyplot as plt
import numpy as np

# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.892] [601] [DEBUG] [Flinger] NotifyHandData request_time_nanos:133352586188, hand_num:2
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.892] [601] [DEBUG] [Flinger] NotifyHandData version:0 hand_data[0].hand_type:0 gesture_type:0 joint_count:26
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.892] [601] [DEBUG] [Flinger] Left hand data version:0 is_tracked:true confidence:1 hand_type:0 gesture_type:0 hand_joint_count:26
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.892] [601] [INFO] [Flinger] Left hand data joint 0 type: 0 x:-0.14877 y:-0.05326 z:-0.32085
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.892] [601] [INFO] [Flinger] Left hand data joint 1 type: 1 x:-0.15626 y:-0.10868 z:-0.31468
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.893] [601] [INFO] [Flinger] Left hand data joint 2 type: 2 x:-0.12357 y:-0.08139 z:-0.32294
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.893] [601] [INFO] [Flinger] Left hand data joint 3 type: 3 x:-0.09237 y:-0.06680 z:-0.32677
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.893] [601] [INFO] [Flinger] Left hand data joint 4 type: 4 x:-0.06829 y:-0.04203 z:-0.33700
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.893] [601] [INFO] [Flinger] Left hand data joint 5 type: 5 x:-0.05804 y:-0.02735 z:-0.34776
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.893] [601] [INFO] [Flinger] Left hand data joint 6 type: 6 x:-0.14211 y:-0.07689 z:-0.32052
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.893] [601] [INFO] [Flinger] Left hand data joint 7 type: 7 x:-0.12071 y:-0.01458 z:-0.32094
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.893] [601] [INFO] [Flinger] Left hand data joint 8 type: 8 x:-0.11199 y:0.02016 z:-0.32507
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.893] [601] [INFO] [Flinger] Left hand data joint 9 type: 9 x:-0.10710 y:0.04201 z:-0.33076
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.893] [601] [INFO] [Flinger] Left hand data joint 10 type: 10 x:-0.10358 y:0.05880 z:-0.33637
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.893] [601] [INFO] [Flinger] Left hand data joint 11 type: 11 x:-0.14846 y:-0.07532 z:-0.32096
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.894] [601] [INFO] [Flinger] Left hand data joint 12 type: 12 x:-0.14340 y:-0.01368 z:-0.32513
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.894] [601] [INFO] [Flinger] Left hand data joint 13 type: 13 x:-0.14334 y:0.02652 z:-0.33011
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.894] [601] [INFO] [Flinger] Left hand data joint 14 type: 14 x:-0.14315 y:0.05122 z:-0.33821
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.894] [601] [INFO] [Flinger] Left hand data joint 15 type: 15 x:-0.14290 y:0.06920 z:-0.34771
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.894] [601] [INFO] [Flinger] Left hand data joint 16 type: 16 x:-0.15885 y:-0.07588 z:-0.32509
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.894] [601] [INFO] [Flinger] Left hand data joint 17 type: 17 x:-0.16110 y:-0.02015 z:-0.33178
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.894] [601] [INFO] [Flinger] Left hand data joint 18 type: 18 x:-0.16590 y:0.01660 z:-0.33558
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.894] [601] [INFO] [Flinger] Left hand data joint 19 type: 19 x:-0.16572 y:0.04016 z:-0.34501
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.894] [601] [INFO] [Flinger] Left hand data joint 20 type: 20 x:-0.16497 y:0.05641 z:-0.35635
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.894] [601] [INFO] [Flinger] Left hand data joint 21 type: 21 x:-0.16816 y:-0.07969 z:-0.33095
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.895] [601] [INFO] [Flinger] Left hand data joint 22 type: 22 x:-0.17716 y:-0.02869 z:-0.33944
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.895] [601] [INFO] [Flinger] Left hand data joint 23 type: 23 x:-0.19001 y:-0.00407 z:-0.34603
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.895] [601] [INFO] [Flinger] Left hand data joint 24 type: 24 x:-0.19429 y:0.01267 z:-0.35450
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.895] [601] [INFO] [Flinger] Left hand data joint 25 type: 25 x:-0.19668 y:0.02513 z:-0.36553
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.895] [601] [DEBUG] [Flinger] NotifyHandData version:0 hand_data[1].hand_type:1 gesture_type:0 joint_count:26
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.895] [601] [DEBUG] [Flinger] Right hand data version:0 is_tracked:true confidence:1 hand_type:1 gesture_type:0 hand_joint_count:26
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.895] [601] [INFO] [Flinger] Right hand data joint 0 type: 0 x:0.11656 y:-0.03313 z:-0.28947
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.895] [601] [INFO] [Flinger] Right hand data joint 1 type: 1 x:0.13549 y:-0.07838 z:-0.26157
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.896] [601] [INFO] [Flinger] Right hand data joint 2 type: 2 x:0.10077 y:-0.06593 z:-0.28526
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.896] [601] [INFO] [Flinger] Right hand data joint 3 type: 3 x:0.07279 y:-0.05892 z:-0.30455
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.896] [601] [INFO] [Flinger] Right hand data joint 4 type: 4 x:0.05091 y:-0.04990 z:-0.33176
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.896] [601] [INFO] [Flinger] Right hand data joint 5 type: 5 x:0.03617 y:-0.04757 z:-0.34589
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.896] [601] [INFO] [Flinger] Right hand data joint 6 type: 6 x:0.11731 y:-0.05551 z:-0.28148
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.896] [601] [INFO] [Flinger] Right hand data joint 7 type: 7 x:0.08329 y:-0.00661 z:-0.30984
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.896] [601] [INFO] [Flinger] Right hand data joint 8 type: 8 x:0.06520 y:0.01991 z:-0.32624
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.896] [601] [INFO] [Flinger] Right hand data joint 9 type: 9 x:0.05468 y:0.03663 z:-0.33821
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.896] [601] [INFO] [Flinger] Right hand data joint 10 type: 10 x:0.04685 y:0.04957 z:-0.34805
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.896] [601] [INFO] [Flinger] Right hand data joint 11 type: 11 x:0.12196 y:-0.05282 z:-0.28119
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.896] [601] [INFO] [Flinger] Right hand data joint 12 type: 12 x:0.10306 y:-0.00079 z:-0.30937
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.896] [601] [INFO] [Flinger] Right hand data joint 13 type: 13 x:0.09194 y:0.03418 z:-0.32621
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.896] [601] [INFO] [Flinger] Right hand data joint 14 type: 14 x:0.08560 y:0.05592 z:-0.33878
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.897] [601] [INFO] [Flinger] Right hand data joint 15 type: 15 x:0.08156 y:0.07199 z:-0.35047
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.897] [601] [INFO] [Flinger] Right hand data joint 16 type: 16 x:0.13287 y:-0.05154 z:-0.28307
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.897] [601] [INFO] [Flinger] Right hand data joint 17 type: 17 x:0.12265 y:-0.00340 z:-0.31040
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.897] [601] [INFO] [Flinger] Right hand data joint 18 type: 18 x:0.11881 y:0.03110 z:-0.32387
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.897] [601] [INFO] [Flinger] Right hand data joint 19 type: 19 x:0.11579 y:0.05284 z:-0.33660
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.897] [601] [INFO] [Flinger] Right hand data joint 20 type: 20 x:0.11408 y:0.06776 z:-0.34952
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.897] [601] [INFO] [Flinger] Right hand data joint 21 type: 21 x:0.14418 y:-0.05395 z:-0.28552
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.897] [601] [INFO] [Flinger] Right hand data joint 22 type: 22 x:0.14208 y:-0.00846 z:-0.31198
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.897] [601] [INFO] [Flinger] Right hand data joint 23 type: 23 x:0.14891 y:0.01780 z:-0.32162
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.897] [601] [INFO] [Flinger] Right hand data joint 24 type: 24 x:0.15150 y:0.03412 z:-0.33180
# Jan 25 00:02:12 XREAL[536]: [2027-01-25 00:02:12.897] [601] [INFO] [Flinger] Right hand data joint 25 type: 25 x:0.15392 y:0.04565 z:-0.34401

# 左手数据点，格式为 [type, x, y, z] - 从日志中提取
left_hand_data = [
    [0, -0.14877, -0.05326, -0.32085],
    [1, -0.15626, -0.10868, -0.31468],
    [2, -0.12357, -0.08139, -0.32294],
    [3, -0.09237, -0.06680, -0.32677],
    [4, -0.06829, -0.04203, -0.33700],
    [5, -0.05804, -0.02735, -0.34776],
    [6, -0.14211, -0.07689, -0.32052],
    [7, -0.12071, -0.01458, -0.32094],
    [8, -0.11199, 0.02016, -0.32507],
    [9, -0.10710, 0.04201, -0.33076],
    [10, -0.10358, 0.05880, -0.33637],
    [11, -0.14846, -0.07532, -0.32096],
    [12, -0.14340, -0.01368, -0.32513],
    [13, -0.14334, 0.02652, -0.33011],
    [14, -0.14315, 0.05122, -0.33821],
    [15, -0.14290, 0.06920, -0.34771],
    [16, -0.15885, -0.07588, -0.32509],
    [17, -0.16110, -0.02015, -0.33178],
    [18, -0.16590, 0.01660, -0.33558],
    [19, -0.16572, 0.04016, -0.34501],
    [20, -0.16497, 0.05641, -0.35635],
    [21, -0.16816, -0.07969, -0.33095],
    [22, -0.17716, -0.02869, -0.33944],
    [23, -0.19001, -0.00407, -0.34603],
    [24, -0.19429, 0.01267, -0.35450],
    [25, -0.19668, 0.02513, -0.36553]
]
# 右手数据点，格式为 [type, x, y, z] - 从日志中提取
right_hand_data = [
    [0, 0.14877, -0.05326, -0.32085],
    [1, 0.15626, -0.10868, -0.31468],
    [2, 0.12357, -0.08139, -0.32294],
    [3, 0.09237, -0.06680, -0.32677],
    [4, 0.06829, -0.04203, -0.33700],
    [5, 0.05804, -0.02735, -0.34776],
    [6, 0.14211, -0.07689, -0.32052],
    [7, 0.12071, -0.01458, -0.32094],
    [8, 0.11199, 0.02016, -0.32507],
    [9, 0.10710, 0.04201, -0.33076],
    [10, 0.10358, 0.05880, -0.33637],
    [11, 0.14846, -0.07532, -0.32096],
    [12, 0.14340, -0.01368, -0.32513],
    [13, 0.14334, 0.02652, -0.33011],
    [14, 0.14315, 0.05122, -0.33821],
    [15, 0.14290, 0.06920, -0.34771],
    [16, 0.15885, -0.07588, -0.32509],
    [17, 0.16110, -0.02015, -0.33178],
    [18, 0.16590, 0.01660, -0.33558],
    [19, 0.16572, 0.04016, -0.34501],
    [20, 0.16497, 0.05641, -0.35635],
    [21, 0.16816, -0.07969, -0.33095],
    [22, 0.17716, -0.02869, -0.33944],
    [23, 0.19001, -0.00407, -0.34603],
    [24, 0.19429, 0.01267, -0.35450],
    [25, 0.19668, 0.02513, -0.36553]
]

# 提取左手坐标和类型
left_types = [point[0] for point in left_hand_data]
left_x_coords = [point[1] for point in left_hand_data]
left_y_coords = [point[2] for point in left_hand_data]
left_z_coords = [point[3] for point in left_hand_data]

# 提取右手坐标和类型
right_types = [point[0] for point in right_hand_data]
right_x_coords = [point[1] for point in right_hand_data]
right_y_coords = [point[2] for point in right_hand_data]
right_z_coords = [point[3] for point in right_hand_data]

# 创建3D图形
fig = plt.figure(figsize=(15, 8))
ax = fig.add_subplot(111, projection="3d")

# 绘制左手散点图（淡紫色）
left_scatter = ax.scatter(left_x_coords, left_y_coords, left_z_coords,
                         c='plum', s=50, alpha=0.8, label='Left Hand')

# 绘制右手散点图（淡紫色）
right_scatter = ax.scatter(right_x_coords, right_y_coords, right_z_coords,
                          c='plum', s=50, alpha=0.8, label='Right Hand')

# 为左手每个点添加类型标签（红色）
for i, (x, y, z, type_val) in enumerate(zip(left_x_coords, left_y_coords, left_z_coords, left_types)):
    ax.text(x, y, z, f"L{type_val}", color="red", fontsize=10)

# 为右手每个点添加类型标签（红色）
for i, (x, y, z, type_val) in enumerate(zip(right_x_coords, right_y_coords, right_z_coords, right_types)):
    ax.text(x, y, z, f"R{type_val}", color="red", fontsize=10)

# 连接指定的节点对
connections = [
    (0, 2),
    (2, 3),
    (3, 4),
    (4, 5),
    (0, 6),
    (6, 7),
    (7, 8),
    (8, 9),
    (9, 10),
    (0, 11),
    (11, 12),
    (12, 13),
    (13, 14),
    (14, 15),
    (0, 16),
    (16, 17),
    (17, 18),
    (18, 19),
    (19, 20),
    (0, 21),
    (21, 22),
    (22, 23),
    (23, 24),
    (24, 25),
]

# 绘制左手连接
def draw_hand_connections(x_coords, y_coords, z_coords, types, color, linewidth=1.5, alpha=0.6):
    # 先绘制0,1两个点之间的蓝色连线
    type1, type2 = 0, 1
    points_type1 = [
        (x_coords[i], y_coords[i], z_coords[i]) for i, t in enumerate(types) if t == type1
    ]
    points_type2 = [
        (x_coords[i], y_coords[i], z_coords[i]) for i, t in enumerate(types) if t == type2
    ]

    # 连接0和1两个点，使用蓝色线条
    for point1 in points_type1:
        for point2 in points_type2:
            ax.plot(
                [point1[0], point2[0]],
                [point1[1], point2[1]],
                [point1[2], point2[2]],
                "b-",
                linewidth=2,
                alpha=0.8,
            )

    # 绘制其他连接
    for type1, type2 in connections:
        # 找到对应类型的节点
        points_type1 = [
            (x_coords[i], y_coords[i], z_coords[i])
            for i, t in enumerate(types)
            if t == type1
        ]
        points_type2 = [
            (x_coords[i], y_coords[i], z_coords[i])
            for i, t in enumerate(types)
            if t == type2
        ]

        # 连接这两种类型的所有节点
        for point1 in points_type1:
            for point2 in points_type2:
                ax.plot(
                    [point1[0], point2[0]],
                    [point1[1], point2[1]],
                    [point1[2], point2[2]],
                    color,
                    linewidth=linewidth,
                    alpha=alpha,
                )

# 绘制左手连接（0-1用蓝色，其他用绿色）
draw_hand_connections(left_x_coords, left_y_coords, left_z_coords, left_types, "green")

# 绘制右手连接（0-1用蓝色，其他用绿色）
draw_hand_connections(right_x_coords, right_y_coords, right_z_coords, right_types, "green")

# 颜色条已移除

# 设置坐标轴标签
ax.set_xlabel("X Axis")
ax.set_ylabel("Y Axis")
ax.set_zlabel("Z Axis")
ax.set_title("3D Visualization of Left and Right Hand Joints (26 points each)")

# 添加图例
ax.legend()

# 设置坐标轴比例相等
ax.set_box_aspect([1, 1, 1])  # 设置xyz轴的比例为1:1:1

# 调整视角以获得更好的视角
ax.view_init(elev=20, azim=45)

# 显示图形
plt.tight_layout()
plt.show()
