#!/usr/bin/env python3
"""
显示Flinger modify_dp_cost日志数据的直方图分析
专门用于显示图表，不保存文件
"""

import re
import matplotlib.pyplot as plt
import numpy as np
import argparse

def parse_flinger_log(log_file):
    """解析日志文件，提取modify_dp_cost数据，只统计前10分钟"""

    histograms = []
    timestamps = []
    bin_params = None
    first_timestamp = None

    # 尝试不同编码读取文件
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        try:
            with open(log_file, 'r', encoding='latin-1') as f:
                content = f.read()
        except:
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

    # 查找所有匹配的行
    lines = content.split('\n')
    for line in lines:
        if '[Flinger] [modify_dp_cost]' in line:
            # 提取时间戳
            timestamp_match = re.search(r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\]', line)
            if timestamp_match:
                from datetime import datetime
                current_timestamp = datetime.strptime(timestamp_match.group(1), '%Y-%m-%d %H:%M:%S.%f')

                # 设置第一个时间戳
                if first_timestamp is None:
                    first_timestamp = current_timestamp

                # 检查是否在前10分钟内
                time_diff = (current_timestamp - first_timestamp).total_seconds()
                if time_diff > 600:  # 10分钟 = 600秒
                    break  # 超过10分钟，停止处理

                timestamps.append(timestamp_match.group(1))

            # 提取bin参数 [0,5000000,500000] - 在括号内
            bin_match = re.search(r'\[(\d+),(\d+),(\d+)\]\)', line)
            if bin_match:
                current_bin_params = [int(bin_match.group(1)), int(bin_match.group(2)), int(bin_match.group(3))]
                if bin_params is None:
                    bin_params = current_bin_params
                elif bin_params != current_bin_params:
                    print(f"Warning: Inconsistent bin parameters found: {current_bin_params} vs {bin_params}")

            # 提取直方图数据 [689,10,0,0,0,0,0,0,0,0] - 在括号后面
            hist_match = re.search(r'\]\) \[([^\]]+)\] in 10sec', line)
            if hist_match:
                try:
                    hist_values = [int(x.strip()) for x in hist_match.group(1).split(',')]
                    if len(hist_values) == 10:  # 确保是10个bin
                        histograms.append(hist_values)
                except ValueError:
                    continue

    return histograms, timestamps, bin_params

def display_analysis(histograms, bin_params, timestamps):
    """显示直方图分析"""
    
    if not histograms or bin_params is None:
        print("没有找到有效的数据！")
        return
    
    print(f"找到 {len(histograms)} 条有效记录 (前10分钟)")
    print(f"Bin参数: 起始={bin_params[0]}, 结束={bin_params[1]}, 宽度={bin_params[2]}")

    # 转换为numpy数组并计算总数
    data = np.array(histograms)
    total_hist = np.sum(data, axis=0)  # 计算每个bin的总数

    # 计算bin的边界
    start, end, width = bin_params

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 总数直方图
    fig, ax = plt.subplots(1, 1, figsize=(10, 6))

    ax.bar(range(10), total_hist, alpha=0.7, color='skyblue', edgecolor='black')
    ax.set_title('Flinger modify_dp_cost 总数直方图 (前10分钟)', fontsize=14)
    ax.set_xlabel('时间范围')
    ax.set_ylabel('总计数')
    ax.grid(True, alpha=0.3)

    # 添加bin范围标签 (转换为毫秒)
    bin_labels = [f'[{(start + i*width)/1000000:.1f},\n{(start + (i+1)*width)/1000000:.1f})ms' for i in range(10)]
    ax.set_xticks(range(10))
    ax.set_xticklabels(bin_labels, rotation=45, ha='right', fontsize=8)

    # 在每个柱子上显示数值
    for i, v in enumerate(total_hist):
        ax.text(i, v + max(total_hist) * 0.01, str(v), ha='center', va='bottom', fontsize=9)
    
    plt.tight_layout()
    plt.show()

def print_statistics(histograms, bin_params):
    """打印统计信息"""
    if not histograms or bin_params is None:
        return

    data = np.array(histograms)
    start, end, width = bin_params

    print("\n=== 统计信息 (前10分钟) ===")
    print(f"总样本数: {len(histograms)}")
    print(f"Bin参数: 起始={start}, 结束={end}, 宽度={width}")
    print(f"每个样本有 {data.shape[1]} 个bin")

    # 计算总数直方图
    total_hist = np.sum(data, axis=0)

    print("\n各bin总数统计 (时间单位: 毫秒):")
    for i in range(data.shape[1]):
        bin_start_ms = (start + i * width) / 1000000
        bin_end_ms = (start + (i + 1) * width) / 1000000
        total_count = total_hist[i]
        percentage = (total_count / np.sum(total_hist)) * 100
        print(f"Bin {i:2d} [{bin_start_ms:4.1f}-{bin_end_ms:4.1f})ms: "
              f"总数={total_count:5d}, "
              f"占比={percentage:5.1f}%")

    # 总计数统计
    total_all = np.sum(total_hist)
    print(f"\n总体统计:")
    print(f"所有事件总数: {total_all}")
    print(f"样本数量: {len(histograms)}")
    print(f"平均每样本事件数: {total_all / len(histograms):.1f}")

    # 性能分析
    fast_events = total_hist[0] + total_hist[1]  # 前两个bin (0-1ms)
    print(f"\n性能分析:")
    print(f"1ms内完成的事件: {fast_events} ({fast_events/total_all*100:.1f}%)")
    print(f"0.5ms内完成的事件: {total_hist[0]} ({total_hist[0]/total_all*100:.1f}%)")

def main():
    parser = argparse.ArgumentParser(description='从pilot.log中分析modify_dp_cost(画准芯)的耗时分布')
    parser.add_argument('log_file', help='pilot.log文件的完整路径')

    args = parser.parse_args()
    
    print(f"正在分析日志文件: {args.log_file}")
    
    try:
        histograms, timestamps, bin_params = parse_flinger_log(args.log_file)
        
        if histograms and bin_params:
            print_statistics(histograms, bin_params)
            display_analysis(histograms, bin_params, timestamps)
            print("\n分析完成！图表已显示。")
        else:
            print("没有找到有效的modify_dp_cost数据")
            
    except FileNotFoundError:
        print(f"错误: 找不到文件 {args.log_file}")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main()
