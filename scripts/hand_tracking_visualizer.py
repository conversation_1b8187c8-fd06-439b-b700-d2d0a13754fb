#!/usr/bin/env python3
"""
Hand Tracking 3D Position Visualizer

This script analyzes hand_in_world.log file and visualizes the 3D position
changes of hand0 and hand1 over time.
"""

import re
import pandas as pd
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import numpy as np
from datetime import datetime
import seaborn as sns

def parse_log_file(filename):
    """
    Parse the hand tracking log file and extract position data.
    
    Args:
        filename (str): Path to the log file
        
    Returns:
        pandas.DataFrame: DataFrame containing parsed data
    """
    data = []
    
    # Regular expression to match log entries
    pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\].*hand_data(\d+) in world x:([-\d\.]+) y:([-\d\.]+) z:([-\d\.]+)'
    
    with open(filename, 'r') as file:
        for line in file:
            match = re.search(pattern, line)
            if match:
                timestamp_str = match.group(1)
                hand_id = int(match.group(2))
                x = float(match.group(3))
                y = float(match.group(4))
                z = float(match.group(5))
                
                # Parse timestamp
                timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S.%f')
                
                data.append({
                    'timestamp': timestamp,
                    'hand_id': hand_id,
                    'x': x,
                    'y': y,
                    'z': z
                })
    
    return pd.DataFrame(data)

def create_visualizations(df):
    """
    Create various visualizations for hand tracking data.
    
    Args:
        df (pandas.DataFrame): DataFrame containing hand tracking data
    """
    # Set up the plotting style
    plt.style.use('seaborn-v0_8')
    sns.set_palette("husl")
    
    # Create figure with subplots
    fig = plt.figure(figsize=(20, 15))
    
    # 1. 3D Trajectory Plot
    ax1 = fig.add_subplot(2, 3, 1, projection='3d')
    
    for hand_id in df['hand_id'].unique():
        hand_data = df[df['hand_id'] == hand_id]
        ax1.plot(hand_data['x'], hand_data['y'], hand_data['z'], 
                label=f'Hand {hand_id}', linewidth=2, alpha=0.7)
        
        # Mark start and end points
        ax1.scatter(hand_data['x'].iloc[0], hand_data['y'].iloc[0], hand_data['z'].iloc[0], 
                   s=100, marker='o', label=f'Hand {hand_id} Start')
        ax1.scatter(hand_data['x'].iloc[-1], hand_data['y'].iloc[-1], hand_data['z'].iloc[-1], 
                   s=100, marker='s', label=f'Hand {hand_id} End')
    
    ax1.set_xlabel('X Position')
    ax1.set_ylabel('Y Position')
    ax1.set_zlabel('Z Position')
    ax1.set_title('3D Hand Trajectories in World Space')
    ax1.legend()
    
    # 2. X Position over Time
    ax2 = fig.add_subplot(2, 3, 2)
    for hand_id in df['hand_id'].unique():
        hand_data = df[df['hand_id'] == hand_id]
        ax2.plot(hand_data['timestamp'], hand_data['x'], 
                label=f'Hand {hand_id}', linewidth=2, alpha=0.8)
    
    ax2.set_xlabel('Time')
    ax2.set_ylabel('X Position')
    ax2.set_title('X Position Over Time')
    ax2.legend()
    ax2.tick_params(axis='x', rotation=45)
    
    # 3. Y Position over Time
    ax3 = fig.add_subplot(2, 3, 3)
    for hand_id in df['hand_id'].unique():
        hand_data = df[df['hand_id'] == hand_id]
        ax3.plot(hand_data['timestamp'], hand_data['y'], 
                label=f'Hand {hand_id}', linewidth=2, alpha=0.8)
    
    ax3.set_xlabel('Time')
    ax3.set_ylabel('Y Position')
    ax3.set_title('Y Position Over Time')
    ax3.legend()
    ax3.tick_params(axis='x', rotation=45)
    
    # 4. Z Position over Time
    ax4 = fig.add_subplot(2, 3, 4)
    for hand_id in df['hand_id'].unique():
        hand_data = df[df['hand_id'] == hand_id]
        ax4.plot(hand_data['timestamp'], hand_data['z'], 
                label=f'Hand {hand_id}', linewidth=2, alpha=0.8)
    
    ax4.set_xlabel('Time')
    ax4.set_ylabel('Z Position')
    ax4.set_title('Z Position Over Time')
    ax4.legend()
    ax4.tick_params(axis='x', rotation=45)
    
    # 5. Distance from Origin over Time
    ax5 = fig.add_subplot(2, 3, 5)
    for hand_id in df['hand_id'].unique():
        hand_data = df[df['hand_id'] == hand_id]
        distance = np.sqrt(hand_data['x']**2 + hand_data['y']**2 + hand_data['z']**2)
        ax5.plot(hand_data['timestamp'], distance, 
                label=f'Hand {hand_id}', linewidth=2, alpha=0.8)
    
    ax5.set_xlabel('Time')
    ax5.set_ylabel('Distance from Origin')
    ax5.set_title('Distance from Origin Over Time')
    ax5.legend()
    ax5.tick_params(axis='x', rotation=45)
    
    # 6. XY Plane Projection
    ax6 = fig.add_subplot(2, 3, 6)
    for hand_id in df['hand_id'].unique():
        hand_data = df[df['hand_id'] == hand_id]
        ax6.plot(hand_data['x'], hand_data['y'], 
                label=f'Hand {hand_id}', linewidth=2, alpha=0.7)
        
        # Mark start and end points
        ax6.scatter(hand_data['x'].iloc[0], hand_data['y'].iloc[0], 
                   s=100, marker='o', label=f'Hand {hand_id} Start')
        ax6.scatter(hand_data['x'].iloc[-1], hand_data['y'].iloc[-1], 
                   s=100, marker='s', label=f'Hand {hand_id} End')
    
    ax6.set_xlabel('X Position')
    ax6.set_ylabel('Y Position')
    ax6.set_title('Hand Movement in XY Plane')
    ax6.legend()
    ax6.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('hand_tracking_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def print_statistics(df):
    """
    Print statistical summary of the hand tracking data.
    
    Args:
        df (pandas.DataFrame): DataFrame containing hand tracking data
    """
    print("=" * 60)
    print("HAND TRACKING DATA ANALYSIS")
    print("=" * 60)
    
    print(f"\nTotal data points: {len(df)}")
    print(f"Time range: {df['timestamp'].min()} to {df['timestamp'].max()}")
    print(f"Duration: {df['timestamp'].max() - df['timestamp'].min()}")
    
    for hand_id in sorted(df['hand_id'].unique()):
        hand_data = df[df['hand_id'] == hand_id]
        print(f"\n--- Hand {hand_id} Statistics ---")
        print(f"Data points: {len(hand_data)}")
        
        print(f"\nPosition ranges:")
        print(f"  X: {hand_data['x'].min():.3f} to {hand_data['x'].max():.3f} (range: {hand_data['x'].max() - hand_data['x'].min():.3f})")
        print(f"  Y: {hand_data['y'].min():.3f} to {hand_data['y'].max():.3f} (range: {hand_data['y'].max() - hand_data['y'].min():.3f})")
        print(f"  Z: {hand_data['z'].min():.3f} to {hand_data['z'].max():.3f} (range: {hand_data['z'].max() - hand_data['z'].min():.3f})")
        
        # Calculate movement statistics
        distances = np.sqrt(np.diff(hand_data['x'])**2 + np.diff(hand_data['y'])**2 + np.diff(hand_data['z'])**2)
        total_distance = np.sum(distances)
        
        print(f"\nMovement statistics:")
        print(f"  Total distance traveled: {total_distance:.3f}")
        print(f"  Average step size: {np.mean(distances):.3f}")
        print(f"  Max step size: {np.max(distances):.3f}")
        
        # Distance from origin
        origin_distances = np.sqrt(hand_data['x']**2 + hand_data['y']**2 + hand_data['z']**2)
        print(f"  Average distance from origin: {np.mean(origin_distances):.3f}")
        print(f"  Min distance from origin: {np.min(origin_distances):.3f}")
        print(f"  Max distance from origin: {np.max(origin_distances):.3f}")

def main():
    """
    Main function to run the hand tracking analysis.
    """
    try:
        # Parse the log file
        print("Parsing hand_in_world.log file...")
        df = parse_log_file('hand_in_world.log')
        
        if df.empty:
            print("No data found in the log file!")
            return
        
        print(f"Successfully parsed {len(df)} data points")
        
        # Print statistics
        print_statistics(df)
        
        # Create visualizations
        print("\nCreating visualizations...")
        create_visualizations(df)
        
        print("\nAnalysis complete! Check 'hand_tracking_analysis.png' for the visualization.")
        
    except FileNotFoundError:
        print("Error: hand_in_world.log file not found!")
        print("Please make sure the file is in the same directory as this script.")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
