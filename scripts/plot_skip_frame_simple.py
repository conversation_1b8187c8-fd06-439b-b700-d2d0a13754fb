#!/usr/bin/env python3
"""
从pilot.log中提取skip_frame数据并绘制折线图
"""

import re
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import argparse
import sys
import os

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans', 'Liberation Sans']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

def extract_and_plot(log_file_path):
    """提取数据并绘图"""
    timestamps = []
    skip_frame_values = []

    # 检查文件是否存在
    if not os.path.exists(log_file_path):
        print(f"错误: 文件 '{log_file_path}' 不存在")
        return

    # 正则表达式匹配skip_frame行
    pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\].*?skip_frame:(\d+)'

    # 尝试不同编码读取文件
    encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']

    start_time = None  # 记录第一个数据点的时间
    ten_minutes = timedelta(minutes=10)  # 10分钟时间间隔

    for encoding in encodings:
        try:
            print(f"尝试编码: {encoding}")
            with open(log_file_path, 'r', encoding=encoding) as file:
                for line in file:
                    if 'skip_frame:' in line:
                        match = re.search(pattern, line)
                        if match:
                            timestamp_str = match.group(1)
                            skip_frame = int(match.group(2))

                            try:
                                dt = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S.%f')

                                # 设置起始时间
                                if start_time is None:
                                    start_time = dt

                                # 只处理前10分钟的数据
                                if dt - start_time <= ten_minutes:
                                    timestamps.append(dt)
                                    skip_frame_values.append(skip_frame)
                                else:
                                    # 超过10分钟，停止处理
                                    print(f"已处理前10分钟数据，停止读取")
                                    break

                            except ValueError:
                                continue
            
            print(f"成功提取 {len(timestamps)} 个数据点")
            break
            
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"读取文件出错: {e}")
            return
    
    if not timestamps:
        print("未找到skip_frame数据")
        return
    
    # 绘制图表
    plt.figure(figsize=(15, 8))
    
    # 绘制折线图
    plt.plot(timestamps, skip_frame_values, 'b-', linewidth=1.5, alpha=0.8, label='skip_frame')
    
    # 添加散点
    plt.scatter(timestamps, skip_frame_values, c='red', s=15, alpha=0.6, zorder=5)
    
    # 设置标题和标签
    plt.title('Skip Frame Over Time Trend', fontsize=16, fontweight='bold')
    plt.xlabel('Time', fontsize=12)
    plt.ylabel('Skip Frame Count', fontsize=12)
    
    # 设置网格
    plt.grid(True, alpha=0.3, linestyle='--')
    
    # 格式化时间轴
    ax = plt.gca()
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
    ax.xaxis.set_major_locator(mdates.MinuteLocator(interval=2))
    plt.xticks(rotation=45)
    
    # 设置y轴从0开始
    plt.ylim(bottom=0)

    # 获取10分钟内最后的skip frame累计数量（因为log中已经是累计值）
    total_skip_frames = skip_frame_values[-1] if skip_frame_values else 0

    # 显示数据点数量和总skip frame数量
    stats_text = f'Data Points: {len(skip_frame_values)}\n10分钟总Skip Frame数量: {total_skip_frames}'
    plt.text(0.02, 0.98, stats_text, transform=ax.transAxes,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('skip_frame_plot.png', dpi=300, bbox_inches='tight')
    print("Chart saved as: skip_frame_plot.png")
    
    # 显示图表
    plt.show()
    
    # 打印基本信息
    print(f"\nBasic Info:")
    print(f"Total data points: {len(skip_frame_values)}")
    print(f"Time range: {timestamps[0].strftime('%H:%M:%S')} to {timestamps[-1].strftime('%H:%M:%S')}")

def main():
    """主函数，处理命令行参数"""
    parser = argparse.ArgumentParser(description='从pilot.log中提取skip_frame数据并绘制折线图（前10分钟）')
    parser.add_argument('log_file', help='pilot.log文件的完整路径')

    args = parser.parse_args()

    print(f"正在分析文件: {args.log_file}")
    print("注意: 只分析前10分钟的数据")

    extract_and_plot(args.log_file)

if __name__ == "__main__":
    main()
