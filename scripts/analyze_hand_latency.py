#!/usr/bin/env python3
"""
分析hand_data_latency日志的Python脚本
从命令行读取log文件路径，画出hand_data_latency日志行的第一个float随时间变化的折线图
第一个float的含义是"最近10秒内hand_data_latency的平均值"
"""

import argparse
import re
from datetime import datetime
import sys

# 尝试导入matplotlib，如果失败则提供安装提示
try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("警告: matplotlib未安装，将只输出数据分析结果，不绘制图表")
    print("要安装matplotlib，请运行: pip3 install matplotlib")

plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'Heiti TC', 'STHeiti', 'PingFang TC']
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号

def parse_log_file(log_file_path):
    """
    解析log文件，提取hand_data_latency数据
    
    Args:
        log_file_path: log文件路径
        
    Returns:
        tuple: (时间列表, 延迟值列表)
    """
    timestamps = []
    latency_values = []
    
    # 正则表达式匹配hand_data_latency行
    # 格式: Jan 25 00:01:10 XREAL[529]: [2027-01-25 00:01:10.128] [789] [DEBUG] [Flinger] [hand_data_latency] 96.384 96.580 (77.338,118.211) 474 times in 10sec
    pattern = r'(\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2}).*?\[(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}\.\d{3})\].*?\[hand_data_latency\]\s+(\d+\.\d+)\s+\d+\.\d+.*?(\d+)\s+times\s+in\s+10sec'
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as file:
            for line_num, line in enumerate(file, 1):
                if '[hand_data_latency]' in line:
                    match = re.search(pattern, line)
                    if match:
                        # 提取时间戳（使用更精确的时间戳）
                        timestamp_str = match.group(2)  # 2027-01-25 00:01:10.128
                        latency_value = float(match.group(3))  # 第一个float值
                        sample_count = int(match.group(4))  # 采样次数
                        
                        try:
                            # 解析时间戳
                            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S.%f')
                            timestamps.append(timestamp)
                            latency_values.append(latency_value)
                            
                            print(f"解析第{line_num}行: {timestamp_str} -> 延迟: {latency_value}ms (采样{sample_count}次)")
                            
                        except ValueError as e:
                            print(f"警告: 第{line_num}行时间戳解析失败: {timestamp_str}, 错误: {e}")
                            continue
                    else:
                        print(f"警告: 第{line_num}行包含hand_data_latency但格式不匹配: {line.strip()}")
                        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {log_file_path}")
        sys.exit(1)
    except Exception as e:
        print(f"错误: 读取文件时发生异常: {e}")
        sys.exit(1)
    
    return timestamps, latency_values

def print_statistics(timestamps, latency_values, log_file_path):
    """
    打印延迟数据的统计信息

    Args:
        timestamps: 时间戳列表
        latency_values: 延迟值列表
        log_file_path: log文件路径
    """
    if not timestamps:
        print("错误: 没有找到有效的hand_data_latency数据")
        sys.exit(1)

    print(f"\n=== Hand Data Latency 分析结果 ===")
    print(f"文件: {log_file_path}")
    print(f"数据点数: {len(latency_values)}")

    if latency_values:
        avg_latency = sum(latency_values) / len(latency_values)
        min_latency = min(latency_values)
        max_latency = max(latency_values)

        print(f"平均延迟: {avg_latency:.2f}ms")
        print(f"最小延迟: {min_latency:.2f}ms")
        print(f"最大延迟: {max_latency:.2f}ms")
        print(f"延迟范围: {max_latency - min_latency:.2f}ms")

        # 时间范围
        if len(timestamps) > 1:
            time_span = timestamps[-1] - timestamps[0]
            print(f"时间跨度: {time_span}")
            print(f"开始时间: {timestamps[0].strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
            print(f"结束时间: {timestamps[-1].strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")

        # 显示前几个和后几个数据点
        print(f"\n前5个数据点:")
        for i in range(min(5, len(timestamps))):
            print(f"  {timestamps[i].strftime('%H:%M:%S.%f')[:-3]}: {latency_values[i]:.2f}ms")

        if len(timestamps) > 5:
            print(f"\n后5个数据点:")
            for i in range(max(0, len(timestamps)-5), len(timestamps)):
                print(f"  {timestamps[i].strftime('%H:%M:%S.%f')[:-3]}: {latency_values[i]:.2f}ms")

def plot_latency_data(timestamps, latency_values, log_file_path):
    """
    绘制延迟数据的折线图

    Args:
        timestamps: 时间戳列表
        latency_values: 延迟值列表
        log_file_path: log文件路径（用于标题）
    """
    if not MATPLOTLIB_AVAILABLE:
        print("\n无法绘制图表，matplotlib未安装")
        return

    if not timestamps:
        print("错误: 没有找到有效的hand_data_latency数据")
        sys.exit(1)

    # 创建图表
    plt.figure(figsize=(12, 6))
    plt.plot(timestamps, latency_values, 'b-', linewidth=1.5, marker='o', markersize=3)

    # 设置标题和标签
    plt.title(f'Hand Data Latency Over Time\n文件: {log_file_path}', fontsize=14, pad=20)
    plt.xlabel('时间', fontsize=12)
    plt.ylabel('延迟 (ms)', fontsize=12)

    # 格式化x轴时间显示
    plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
    plt.gca().xaxis.set_major_locator(mdates.SecondLocator(interval=10))
    plt.xticks(rotation=45)

    # 添加网格
    plt.grid(True, alpha=0.3)

    # 添加统计信息
    if latency_values:
        avg_latency = sum(latency_values) / len(latency_values)
        min_latency = min(latency_values)
        max_latency = max(latency_values)

        stats_text = f'统计信息:\n平均延迟: {avg_latency:.2f}ms\n最小延迟: {min_latency:.2f}ms\n最大延迟: {max_latency:.2f}ms\n数据点数: {len(latency_values)}'
        plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    # 调整布局
    plt.tight_layout()

    # 显示图表
    plt.show()

    print(f"\n成功绘制了 {len(latency_values)} 个数据点的延迟图表")

def save_to_csv(timestamps, latency_values, log_file_path):
    """
    将数据保存到CSV文件

    Args:
        timestamps: 时间戳列表
        latency_values: 延迟值列表
        log_file_path: log文件路径
    """
    import csv
    import os

    # 生成CSV文件名
    base_name = os.path.splitext(os.path.basename(log_file_path))[0]
    csv_file = f"{base_name}_hand_latency.csv"

    try:
        with open(csv_file, 'w', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)
            writer.writerow(['时间戳', '延迟(ms)', '时间字符串'])

            for timestamp, latency in zip(timestamps, latency_values):
                writer.writerow([
                    timestamp.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],
                    latency,
                    timestamp.strftime('%H:%M:%S.%f')[:-3]
                ])

        print(f"\n数据已保存到CSV文件: {csv_file}")

    except Exception as e:
        print(f"保存CSV文件时出错: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='分析hand_data_latency日志文件')
    parser.add_argument('log_file', help='log文件路径')
    parser.add_argument('--csv', action='store_true', help='保存数据到CSV文件')

    args = parser.parse_args()

    print(f"开始分析log文件: {args.log_file}")
    print("正在解析hand_data_latency数据...")

    # 解析log文件
    timestamps, latency_values = parse_log_file(args.log_file)

    # 打印统计信息
    print_statistics(timestamps, latency_values, args.log_file)

    # 保存到CSV（如果指定）
    if args.csv:
        save_to_csv(timestamps, latency_values, args.log_file)

    # 绘制图表（如果matplotlib可用）
    if MATPLOTLIB_AVAILABLE:
        plot_latency_data(timestamps, latency_values, args.log_file)
    else:
        print("\n要查看图表，请安装matplotlib: pip3 install matplotlib")
        print("或者使用 --csv 选项将数据导出到CSV文件，然后在Excel等工具中查看")

if __name__ == '__main__':
    main()
