#!/usr/bin/env python3
"""
Convert RGB planar binary data to PNG image.

RGB planar format stores color channels separately:
- R plane: width * height bytes
- G plane: width * height bytes  
- B plane: width * height bytes

Usage:
    python convert_rgb_planar_to_png.py input.bin output.png width height [--data-type uint8]
"""

import numpy as np
from PIL import Image
import argparse
import os
from datetime import datetime


def rgb_planar_to_rgb_interleaved(rgb_planar_data, width, height, data_type=np.uint8):
    """
    Convert RGB planar data to RGB interleaved format.
    
    Args:
        rgb_planar_data: Raw binary data containing R, G, B planes
        width: Image width in pixels
        height: Image height in pixels
        data_type: Data type of the input data (default: np.uint8)
    
    Returns:
        RGB image data as numpy array with shape (height, width, 3)
    """
    frame_size = width * height
    expected_size = frame_size * 3
    
    if data_type == np.uint8:
        bytes_per_pixel = 1
    elif data_type == np.uint16:
        bytes_per_pixel = 2
    elif data_type == np.float32:
        bytes_per_pixel = 4
    else:
        raise ValueError(f"Unsupported data type: {data_type}")
    
    expected_bytes = expected_size * bytes_per_pixel
    
    if len(rgb_planar_data) != expected_bytes:
        raise ValueError(f"Data size mismatch. Expected {expected_bytes} bytes for {width}x{height} RGB planar image, got {len(rgb_planar_data)} bytes")
    
    # Convert to numpy array with specified data type
    data_array = np.frombuffer(rgb_planar_data, dtype=data_type)
    
    # Extract R, G, and B planes
    r_plane = data_array[0:frame_size].reshape((height, width))
    g_plane = data_array[frame_size:frame_size * 2].reshape((height, width))
    b_plane = data_array[frame_size * 2:frame_size * 3].reshape((height, width))
    
    # Stack planes to create RGB image
    rgb_image = np.stack((r_plane, g_plane, b_plane), axis=-1)
    
    # Convert to uint8 if necessary
    if data_type != np.uint8:
        if data_type == np.uint16:
            # Convert 16-bit to 8-bit by scaling
            rgb_image = (rgb_image / 256).astype(np.uint8)
        elif data_type == np.float32:
            # Assume float values are in range [0, 1] or [0, 255]
            if rgb_image.max() <= 1.0:
                rgb_image = (rgb_image * 255).astype(np.uint8)
            else:
                rgb_image = np.clip(rgb_image, 0, 255).astype(np.uint8)
    
    return rgb_image


def save_rgb_image(rgb_data, output_path):
    """
    Save RGB image data as PNG file.
    
    Args:
        rgb_data: RGB image data as numpy array
        output_path: Output PNG file path
    """
    image = Image.fromarray(rgb_data, 'RGB')
    image.save(output_path)
    print(f"Successfully saved PNG image to: {output_path}")


def get_timestamped_filename(base_path):
    """
    Generate a timestamped filename if the output file already exists.
    
    Args:
        base_path: Base output file path
        
    Returns:
        Timestamped filename if original exists, otherwise original filename
    """
    if not os.path.exists(base_path):
        return base_path
    
    # Generate timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Split path and add timestamp
    path_parts = os.path.splitext(base_path)
    timestamped_path = f"{path_parts[0]}_{timestamp}{path_parts[1]}"
    
    return timestamped_path


def main():
    parser = argparse.ArgumentParser(
        description="Convert RGB planar binary data to PNG image.",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    # Convert 8-bit RGB planar data
    python convert_rgb_planar_to_png.py input.bin output.png 1920 1080
    
    # Convert 16-bit RGB planar data
    python convert_rgb_planar_to_png.py input.bin output.png 1920 1080 --data-type uint16
    
    # Convert float32 RGB planar data
    python convert_rgb_planar_to_png.py input.bin output.png 1920 1080 --data-type float32
        """
    )
    
    parser.add_argument('input_file', help="Path to the input RGB planar binary file")
    parser.add_argument('output_file', help="Path to the output PNG file")
    parser.add_argument('width', type=int, help="Width of the image in pixels")
    parser.add_argument('height', type=int, help="Height of the image in pixels")
    parser.add_argument('--data-type', choices=['uint8', 'uint16', 'float32'], 
                       default='uint8', help="Data type of the input data (default: uint8)")
    parser.add_argument('--timestamp', action='store_true', 
                       help="Add timestamp to output filename if file exists")
    
    args = parser.parse_args()
    
    # Validate input file
    if not os.path.exists(args.input_file):
        print(f"Error: Input file '{args.input_file}' does not exist")
        return 1
    
    # Convert data type string to numpy type
    data_type_map = {
        'uint8': np.uint8,
        'uint16': np.uint16,
        'float32': np.float32
    }
    data_type = data_type_map[args.data_type]
    
    # Handle output filename
    output_file = args.output_file
    if args.timestamp:
        output_file = get_timestamped_filename(output_file)
    
    try:
        # Read the RGB planar binary file
        print(f"Reading RGB planar data from: {args.input_file}")
        with open(args.input_file, 'rb') as f:
            rgb_planar_data = f.read()
        
        print(f"File size: {len(rgb_planar_data)} bytes")
        print(f"Image dimensions: {args.width}x{args.height}")
        print(f"Data type: {args.data_type}")
        
        # Convert RGB planar to RGB interleaved
        rgb_image = rgb_planar_to_rgb_interleaved(rgb_planar_data, args.width, args.height, data_type)
        
        # Create output directory if it doesn't exist
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"Created output directory: {output_dir}")
        
        # Save the RGB image as PNG
        save_rgb_image(rgb_image, output_file)
        
        return 0
        
    except Exception as e:
        print(f"Error: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
